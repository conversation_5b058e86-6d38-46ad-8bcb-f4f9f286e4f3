package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.service.IEvaluationTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class StudentEvaluationTaskIdTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationTaskService evaluationTaskService;

    @InjectMocks
    private StudentController studentController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(studentController)
                .build();
    }

    @Test
    public void testGetEvaluationTaskId_WhenTaskExists() throws Exception {
        // 准备测试数据
        Long studentId = 1L;
        Long teacherId = 2L;
        Long courseId = 3L;
        Long taskId = 100L;
        
        // 配置Mock行为 - 找到评教任务
        when(evaluationTaskService.getEvaluationTaskId(anyLong(), anyLong(), anyLong())).thenReturn(taskId);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-id")
                .param("studentId", studentId.toString())
                .param("teacherId", teacherId.toString())
                .param("courseId", courseId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评教任务ID成功"))
                .andExpect(jsonPath("$.data.taskId").value("100"));
    }

    @Test
    public void testGetEvaluationTaskId_WhenTaskNotExists() throws Exception {
        // 准备测试数据
        Long studentId = 1L;
        Long teacherId = 2L;
        Long courseId = 3L;
        
        // 配置Mock行为 - 没有找到评教任务
        when(evaluationTaskService.getEvaluationTaskId(anyLong(), anyLong(), anyLong())).thenReturn(null);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-id")
                .param("studentId", studentId.toString())
                .param("teacherId", teacherId.toString())
                .param("courseId", courseId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评教任务ID成功"))
                .andExpect(jsonPath("$.data.taskId").isEmpty());
    }

    @Test
    public void testGetEvaluationTaskId_WhenException() throws Exception {
        // 准备测试数据
        Long studentId = 1L;
        Long teacherId = 2L;
        Long courseId = 3L;
        
        // 配置Mock行为 - 抛出异常
        when(evaluationTaskService.getEvaluationTaskId(anyLong(), anyLong(), anyLong())).thenThrow(new RuntimeException("测试异常"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-id")
                .param("studentId", studentId.toString())
                .param("teacherId", teacherId.toString())
                .param("courseId", courseId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取评教任务ID失败: 测试异常"));
    }

    @Test
    public void testGetEvaluationTaskId_WithMissingParameters() throws Exception {
        // 执行请求并验证结果 - 缺少必需参数
        mockMvc.perform(get("/student/evaluation/task-id")
                .param("studentId", "1")
                .param("teacherId", "2"))
                // 缺少courseId参数
                .andExpect(status().isBadRequest());
    }
}
