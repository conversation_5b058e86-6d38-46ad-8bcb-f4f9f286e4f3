package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.dto.EvaluationDimensionDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationIndicatorDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationScoreOptionDetailDTO;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class StudentEvaluationDimensionsTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationTaskService evaluationTaskService;

    @InjectMocks
    private StudentController studentController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(studentController)
                .build();
    }

    @Test
    public void testGetEvaluationDimensions_WhenHasDimensions() throws Exception {
        // 准备测试数据
        List<EvaluationDimensionDetailDTO> dimensions = new ArrayList<>();

        // 创建维度1
        EvaluationDimensionDetailDTO dimension1 = new EvaluationDimensionDetailDTO();
        dimension1.setDimension_id("1");
        dimension1.setDimension_name("教学态度");
        dimension1.setWeight(new BigDecimal("0.3"));

        // 创建指标1
        EvaluationIndicatorDetailDTO indicator1 = new EvaluationIndicatorDetailDTO();
        indicator1.setIndicator_id("1");
        indicator1.setIndicator_name("备课充分");
        indicator1.setWeight(new BigDecimal("0.5"));
        indicator1.setMax_score(new BigDecimal("10"));
        indicator1.setMin_score(new BigDecimal("0"));
        indicator1.setScoring_type("option");

        // 创建评分选项
        EvaluationScoreOptionDetailDTO option1 = new EvaluationScoreOptionDetailDTO();
        option1.setOption_id("1");
        option1.setScore(new BigDecimal("10"));
        option1.setScore_description("优秀");
        option1.setSort(1);

        EvaluationScoreOptionDetailDTO option2 = new EvaluationScoreOptionDetailDTO();
        option2.setOption_id("2");
        option2.setScore(new BigDecimal("8"));
        option2.setScore_description("良好");
        option2.setSort(2);

        indicator1.setOptions(Arrays.asList(option1, option2));
        dimension1.setIndicators(Arrays.asList(indicator1));

        dimensions.add(dimension1);

        // 配置Mock行为
        when(evaluationTaskService.getCurrentEvaluationDimensions()).thenReturn(dimensions);

        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/dimensions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评价维度详情成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].dimension_id").value("1"))
                .andExpect(jsonPath("$.data[0].dimension_name").value("教学态度"))
                .andExpect(jsonPath("$.data[0].weight").value(0.3))
                .andExpect(jsonPath("$.data[0].indicators").isArray())
                .andExpect(jsonPath("$.data[0].indicators.length()").value(1))
                .andExpect(jsonPath("$.data[0].indicators[0].indicator_id").value("1"))
                .andExpect(jsonPath("$.data[0].indicators[0].indicator_name").value("备课充分"))
                .andExpect(jsonPath("$.data[0].indicators[0].scoring_type").value("option"))
                .andExpect(jsonPath("$.data[0].indicators[0].options").isArray())
                .andExpect(jsonPath("$.data[0].indicators[0].options.length()").value(2))
                .andExpect(jsonPath("$.data[0].indicators[0].options[0].option_id").value("1"))
                .andExpect(jsonPath("$.data[0].indicators[0].options[0].score_description").value("优秀"));
    }

    @Test
    public void testGetEvaluationDimensions_WhenNoDimensions() throws Exception {
        // 配置Mock行为 - 没有评价维度
        when(evaluationTaskService.getCurrentEvaluationDimensions()).thenReturn(new ArrayList<>());

        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/dimensions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评价维度详情成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    public void testGetEvaluationDimensions_WhenException() throws Exception {
        // 配置Mock行为 - 抛出异常
        when(evaluationTaskService.getCurrentEvaluationDimensions()).thenThrow(new RuntimeException("测试异常"));

        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/dimensions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取评价维度详情失败: 测试异常"));
    }
}
