package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.PageResult;
import com.lxxgd.bysj02.dto.EvaluationPeriodDTO;
import com.lxxgd.bysj02.dto.PeriodListParamsDTO;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class EvaluationPeriodControllerTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationPeriodService evaluationPeriodService;

    @InjectMocks
    private EvaluationPeriodController evaluationPeriodController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(evaluationPeriodController)
                .build();
    }

    @Test
    public void testGetPeriodList() throws Exception {
        // 准备测试数据
        List<EvaluationPeriodDTO> periodList = new ArrayList<>();
        
        EvaluationPeriodDTO period1 = new EvaluationPeriodDTO();
        period1.setPeriod_id(1L);
        period1.setSemester("2024-2025学年第一学期");
        period1.setStart_time("2024-09-01 00:00:00");
        period1.setEnd_time("2024-09-30 23:59:59");
        period1.setStatus(1);
        periodList.add(period1);
        
        EvaluationPeriodDTO period2 = new EvaluationPeriodDTO();
        period2.setPeriod_id(2L);
        period2.setSemester("2024-2025学年第二学期");
        period2.setStart_time("2025-03-01 00:00:00");
        period2.setEnd_time("2025-03-31 23:59:59");
        period2.setStatus(0);
        periodList.add(period2);
        
        PageResult<EvaluationPeriodDTO> pageResult = new PageResult<>();
        pageResult.setList(periodList);
        pageResult.setTotal(2);
        
        // 配置Mock行为
        when(evaluationPeriodService.getPeriodList(any(PeriodListParamsDTO.class))).thenReturn(pageResult);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods")
                .param("page", "1")
                .param("limit", "10")
                .param("semester", "2024"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评教周期列表成功"))
                .andExpect(jsonPath("$.data.items").isArray())
                .andExpect(jsonPath("$.data.items.length()").value(2))
                .andExpect(jsonPath("$.data.items[0].period_id").value(1))
                .andExpect(jsonPath("$.data.items[0].semester").value("2024-2025学年第一学期"))
                .andExpect(jsonPath("$.data.items[0].status").value(1))
                .andExpect(jsonPath("$.data.items[1].period_id").value(2))
                .andExpect(jsonPath("$.data.items[1].semester").value("2024-2025学年第二学期"))
                .andExpect(jsonPath("$.data.items[1].status").value(0))
                .andExpect(jsonPath("$.data.total").value(2));
    }
    
    @Test
    public void testGetPeriodListWithEmptyResult() throws Exception {
        // 准备测试数据
        PageResult<EvaluationPeriodDTO> emptyPageResult = new PageResult<>();
        emptyPageResult.setList(new ArrayList<>());
        emptyPageResult.setTotal(0);
        
        // 配置Mock行为
        when(evaluationPeriodService.getPeriodList(any(PeriodListParamsDTO.class))).thenReturn(emptyPageResult);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods")
                .param("page", "1")
                .param("limit", "10")
                .param("semester", "不存在的学期"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评教周期列表成功"))
                .andExpect(jsonPath("$.data.items").isArray())
                .andExpect(jsonPath("$.data.items.length()").value(0))
                .andExpect(jsonPath("$.data.total").value(0));
    }
    
    @Test
    public void testGetPeriodListWithException() throws Exception {
        // 配置Mock行为
        when(evaluationPeriodService.getPeriodList(any(PeriodListParamsDTO.class))).thenThrow(new RuntimeException("数据库连接失败"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("获取评教周期列表失败: 数据库连接失败"));
    }
}
