package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.service.IEvaluationTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class StudentEvaluationTaskStatusTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationTaskService evaluationTaskService;

    @InjectMocks
    private StudentController studentController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(studentController)
                .build();
    }

    @Test
    public void testGetEvaluationTaskStatus_WhenHasCurrentTask() throws Exception {
        // 准备测试数据 - 学生有当前评教任务
        when(evaluationTaskService.hasCurrentEvaluationTask(anyLong())).thenReturn(true);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", 1L))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true))
                .andExpect(jsonPath("$.message").value("学生当前有评教任务"))
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    public void testGetEvaluationTaskStatus_WhenNoCurrentTask() throws Exception {
        // 准备测试数据 - 学生没有当前评教任务
        when(evaluationTaskService.hasCurrentEvaluationTask(anyLong())).thenReturn(false);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", 1L))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(false))
                .andExpect(jsonPath("$.message").value("学生当前没有评教任务"))
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    public void testGetEvaluationTaskStatus_WhenServiceThrowsException() throws Exception {
        // 准备测试数据 - 服务层抛出异常
        when(evaluationTaskService.hasCurrentEvaluationTask(anyLong()))
                .thenThrow(new RuntimeException("数据库连接失败"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", 1L))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取学生评教任务状态失败: 数据库连接失败"));
    }

    @Test
    public void testGetEvaluationTaskStatus_WithDifferentStudentIds() throws Exception {
        // 测试不同的学生ID
        
        // 学生ID 1 - 有任务
        when(evaluationTaskService.hasCurrentEvaluationTask(1L)).thenReturn(true);
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", 1L))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(true));
        
        // 学生ID 2 - 没有任务
        when(evaluationTaskService.hasCurrentEvaluationTask(2L)).thenReturn(false);
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", 2L))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").value(false));
    }

    @Test
    public void testGetEvaluationTaskStatus_WithLargeStudentId() throws Exception {
        // 测试大的学生ID
        Long largeStudentId = 999999999L;
        when(evaluationTaskService.hasCurrentEvaluationTask(largeStudentId)).thenReturn(false);
        
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", largeStudentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(false))
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    public void testGetEvaluationTaskStatus_ResponseFormat() throws Exception {
        // 验证响应格式完全符合要求
        when(evaluationTaskService.hasCurrentEvaluationTask(anyLong())).thenReturn(true);
        
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", 1L))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").exists())
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.success").exists())
                .andExpect(jsonPath("$.code").isNumber())
                .andExpect(jsonPath("$.data").isBoolean())
                .andExpect(jsonPath("$.message").isString())
                .andExpect(jsonPath("$.success").isBoolean());
    }
}
