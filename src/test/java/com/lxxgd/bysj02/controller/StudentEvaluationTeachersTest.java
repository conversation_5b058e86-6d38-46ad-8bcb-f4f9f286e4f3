package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.dto.TeacherInfoDTO;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class StudentEvaluationTeachersTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationTaskService evaluationTaskService;

    @InjectMocks
    private StudentController studentController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(studentController)
                .build();
    }

    @Test
    public void testGetEvaluationTeachers_WhenHasTeachers() throws Exception {
        // 准备测试数据
        Long studentId = 1L;
        List<TeacherInfoDTO> teachers = new ArrayList<>();
        
        TeacherInfoDTO teacher1 = new TeacherInfoDTO();
        teacher1.setTeacherId("1");
        teacher1.setRealName("张三");
        teacher1.setTitle("教授");
        
        TeacherInfoDTO teacher2 = new TeacherInfoDTO();
        teacher2.setTeacherId("2");
        teacher2.setRealName("李四");
        teacher2.setTitle("副教授");
        
        teachers.add(teacher1);
        teachers.add(teacher2);
        
        // 配置Mock行为 - 学生有待评价教师
        when(evaluationTaskService.getEvaluationTeachersByStudentId(anyLong())).thenReturn(teachers);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/teachers/{studentId}", studentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取待评价教师列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].teacherId").value("1"))
                .andExpect(jsonPath("$.data[0].realName").value("张三"))
                .andExpect(jsonPath("$.data[0].title").value("教授"))
                .andExpect(jsonPath("$.data[1].teacherId").value("2"))
                .andExpect(jsonPath("$.data[1].realName").value("李四"))
                .andExpect(jsonPath("$.data[1].title").value("副教授"));
    }

    @Test
    public void testGetEvaluationTeachers_WhenNoTeachers() throws Exception {
        // 准备测试数据
        Long studentId = 2L;
        
        // 配置Mock行为 - 学生没有待评价教师
        when(evaluationTaskService.getEvaluationTeachersByStudentId(anyLong())).thenReturn(new ArrayList<>());
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/teachers/{studentId}", studentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取待评价教师列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    public void testGetEvaluationTeachers_WhenException() throws Exception {
        // 准备测试数据
        Long studentId = 3L;
        
        // 配置Mock行为 - 抛出异常
        when(evaluationTaskService.getEvaluationTeachersByStudentId(anyLong())).thenThrow(new RuntimeException("测试异常"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/teachers/{studentId}", studentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取待评价教师列表失败: 测试异常"));
    }
}
