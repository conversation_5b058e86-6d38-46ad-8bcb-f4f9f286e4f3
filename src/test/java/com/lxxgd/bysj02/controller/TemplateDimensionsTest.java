package com.lxxgd.bysj02.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lxxgd.bysj02.dto.TemplateDimensionDetailDTO;
import com.lxxgd.bysj02.service.ITemplateDimensionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class TemplateDimensionsTest {

    private MockMvc mockMvc;

    @Mock
    private ITemplateDimensionService templateDimensionService;

    @InjectMocks
    private EvaluationTemplateController evaluationTemplateController;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(evaluationTemplateController)
                .build();
    }

    @Test
    public void testGetTemplateDimensions() throws Exception {
        // 准备测试数据
        Long templateId = 1L;
        List<TemplateDimensionDetailDTO> dimensions = new ArrayList<>();
        
        TemplateDimensionDetailDTO dimension1 = new TemplateDimensionDetailDTO();
        dimension1.setId(1L);
        dimension1.setTemplate_id(templateId);
        dimension1.setDimension_id(101L);
        dimension1.setDimension_name("教学态度");
        dimension1.setWeight(new BigDecimal("0.4"));
        dimension1.setSort(1);
        dimensions.add(dimension1);
        
        TemplateDimensionDetailDTO dimension2 = new TemplateDimensionDetailDTO();
        dimension2.setId(2L);
        dimension2.setTemplate_id(templateId);
        dimension2.setDimension_id(102L);
        dimension2.setDimension_name("教学内容");
        dimension2.setWeight(new BigDecimal("0.6"));
        dimension2.setSort(2);
        dimensions.add(dimension2);
        
        // 配置Mock行为
        when(templateDimensionService.getTemplateDimensions(anyLong())).thenReturn(dimensions);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation-templates/{templateId}/dimensions", templateId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取模板维度列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].dimension_id").value(101))
                .andExpect(jsonPath("$.data[0].dimension_name").value("教学态度"))
                .andExpect(jsonPath("$.data[0].weight").value(0.4))
                .andExpect(jsonPath("$.data[0].sort").value(1))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].dimension_id").value(102))
                .andExpect(jsonPath("$.data[1].dimension_name").value("教学内容"))
                .andExpect(jsonPath("$.data[1].weight").value(0.6))
                .andExpect(jsonPath("$.data[1].sort").value(2));
    }
    
    @Test
    public void testGetTemplateDimensionsWithEmptyList() throws Exception {
        // 准备测试数据
        Long templateId = 2L;
        List<TemplateDimensionDetailDTO> emptyDimensions = new ArrayList<>();
        
        // 配置Mock行为
        when(templateDimensionService.getTemplateDimensions(anyLong())).thenReturn(emptyDimensions);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation-templates/{templateId}/dimensions", templateId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取模板维度列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }
    
    @Test
    public void testGetTemplateDimensionsWithException() throws Exception {
        // 准备测试数据
        Long templateId = 999L;
        
        // 配置Mock行为，模拟异常情况
        when(templateDimensionService.getTemplateDimensions(anyLong())).thenThrow(new RuntimeException("模板不存在"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation-templates/{templateId}/dimensions", templateId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("模板不存在"));
    }
}
