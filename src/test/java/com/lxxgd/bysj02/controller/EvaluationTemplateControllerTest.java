package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.PageResult;
import com.lxxgd.bysj02.dto.EvaluationTemplateDTO;
import com.lxxgd.bysj02.dto.TemplateListParamsDTO;
import com.lxxgd.bysj02.service.IEvaluationTemplateService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
public class EvaluationTemplateControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private IEvaluationTemplateService evaluationTemplateService;

    @Test
    public void testGetTemplateList() throws Exception {
        // 准备模拟数据
        List<EvaluationTemplateDTO> templates = new ArrayList<>();
        EvaluationTemplateDTO template = new EvaluationTemplateDTO();
        template.setTemplate_id(1L);
        template.setTemplate_name("测试模板");
        template.setDescription("这是一个测试模板");
        template.setStatus((byte) 1);
        template.setIs_default((byte) 1);
        templates.add(template);

        PageResult<EvaluationTemplateDTO> pageResult = new PageResult<>();
        pageResult.setList(templates);
        pageResult.setTotal(1);

        // 模拟服务层方法
        when(evaluationTemplateService.getTemplateList(any(TemplateListParamsDTO.class))).thenReturn(pageResult);

        // 执行请求并验证结果
        mockMvc.perform(MockMvcRequestBuilders.get("/evaluation-templates")
                        .param("page", "1")
                        .param("size", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.items[0].template_id").value(1))
                .andExpect(jsonPath("$.data.items[0].template_name").value("测试模板"));
    }
}
