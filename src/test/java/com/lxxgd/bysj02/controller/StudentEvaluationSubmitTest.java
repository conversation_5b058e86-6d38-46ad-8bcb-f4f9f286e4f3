package com.lxxgd.bysj02.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lxxgd.bysj02.dto.DimensionScoreDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationRecordDetailDTO;
import com.lxxgd.bysj02.dto.StudentEvaluationSubmissionDTO;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class StudentEvaluationSubmitTest {

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @Mock
    private IEvaluationTaskService evaluationTaskService;

    @InjectMocks
    private StudentController studentController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(studentController)
                .build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testSubmitStudentEvaluation_Success() throws Exception {
        // 准备测试数据
        StudentEvaluationSubmissionDTO submissionDTO = new StudentEvaluationSubmissionDTO();
        submissionDTO.setTask_id("1");
        submissionDTO.setTotal_score(new BigDecimal("85.5"));
        submissionDTO.setComment("整体教学质量很好");

        // 创建评价详情
        EvaluationRecordDetailDTO detail1 = new EvaluationRecordDetailDTO();
        detail1.setIndicator_id("1");
        detail1.setScore(new BigDecimal("9"));
        detail1.setComment("备课充分");

        EvaluationRecordDetailDTO detail2 = new EvaluationRecordDetailDTO();
        detail2.setIndicator_id("2");
        detail2.setScore(new BigDecimal("8"));
        detail2.setComment("讲解清晰");

        List<EvaluationRecordDetailDTO> details = Arrays.asList(detail1, detail2);
        submissionDTO.setDetails(details);

        // 创建维度得分详情
        DimensionScoreDetailDTO dimensionScore1 = new DimensionScoreDetailDTO();
        dimensionScore1.setDimension_id("1");
        dimensionScore1.setScore(new BigDecimal("8.5"));

        DimensionScoreDetailDTO dimensionScore2 = new DimensionScoreDetailDTO();
        dimensionScore2.setDimension_id("2");
        dimensionScore2.setScore(new BigDecimal("9.0"));

        List<DimensionScoreDetailDTO> dimensionScores = Arrays.asList(dimensionScore1, dimensionScore2);
        submissionDTO.setDimensionScores(dimensionScores);

        // 配置Mock行为 - 提交成功
        when(evaluationTaskService.submitStudentEvaluation(any(StudentEvaluationSubmissionDTO.class))).thenReturn(true);

        // 执行请求并验证结果
        mockMvc.perform(post("/student/evaluation/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(submissionDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("评价提交成功"))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    public void testSubmitStudentEvaluation_ValidationError() throws Exception {
        // 准备测试数据 - 缺少必需字段
        StudentEvaluationSubmissionDTO submissionDTO = new StudentEvaluationSubmissionDTO();
        // 缺少task_id和total_score
        submissionDTO.setComment("整体教学质量很好");

        // 执行请求并验证结果 - 应该返回验证错误
        mockMvc.perform(post("/student/evaluation/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(submissionDTO)))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void testSubmitStudentEvaluation_ServiceException() throws Exception {
        // 准备测试数据
        StudentEvaluationSubmissionDTO submissionDTO = new StudentEvaluationSubmissionDTO();
        submissionDTO.setTask_id("1");
        submissionDTO.setTotal_score(new BigDecimal("85.5"));
        submissionDTO.setComment("整体教学质量很好");

        EvaluationRecordDetailDTO detail = new EvaluationRecordDetailDTO();
        detail.setIndicator_id("1");
        detail.setScore(new BigDecimal("9"));
        detail.setComment("备课充分");

        submissionDTO.setDetails(Arrays.asList(detail));

        // 添加维度得分详情
        DimensionScoreDetailDTO dimensionScore = new DimensionScoreDetailDTO();
        dimensionScore.setDimension_id("1");
        dimensionScore.setScore(new BigDecimal("8.5"));
        submissionDTO.setDimensionScores(Arrays.asList(dimensionScore));

        // 配置Mock行为 - 抛出异常
        when(evaluationTaskService.submitStudentEvaluation(any(StudentEvaluationSubmissionDTO.class)))
                .thenThrow(new RuntimeException("评教任务不存在"));

        // 执行请求并验证结果
        mockMvc.perform(post("/student/evaluation/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(submissionDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("提交评价失败: 评教任务不存在"));
    }

    @Test
    public void testSubmitStudentEvaluation_EmptyDetails() throws Exception {
        // 准备测试数据 - 评价详情为空
        StudentEvaluationSubmissionDTO submissionDTO = new StudentEvaluationSubmissionDTO();
        submissionDTO.setTask_id("1");
        submissionDTO.setTotal_score(new BigDecimal("85.5"));
        submissionDTO.setComment("整体教学质量很好");
        submissionDTO.setDetails(Arrays.asList()); // 空列表
        // dimensionScores字段为null，应该触发验证错误

        // 执行请求并验证结果 - 应该返回验证错误
        mockMvc.perform(post("/student/evaluation/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(submissionDTO)))
                .andExpect(status().isBadRequest());
    }
}
