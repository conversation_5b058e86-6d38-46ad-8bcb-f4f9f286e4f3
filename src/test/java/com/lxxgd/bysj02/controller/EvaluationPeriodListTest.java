package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.PageResult;
import com.lxxgd.bysj02.dto.EvaluationPeriodDTO;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class EvaluationPeriodListTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationPeriodService evaluationPeriodService;

    @InjectMocks
    private EvaluationPeriodController evaluationPeriodController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(evaluationPeriodController)
                .build();
    }

    @Test
    public void testGetPeriodList_WhenDataExists() throws Exception {
        // 准备测试数据
        List<EvaluationPeriodDTO> periods = new ArrayList<>();
        
        EvaluationPeriodDTO period1 = new EvaluationPeriodDTO();
        period1.setPeriod_id(1L);
        period1.setSemester("2024-2025学年第二学期");
        period1.setStart_time("2025-05-20 00:00:00");
        period1.setEnd_time("2025-06-01 23:59:59");
        period1.setStatus(1); // 进行中
        
        EvaluationPeriodDTO period2 = new EvaluationPeriodDTO();
        period2.setPeriod_id(2L);
        period2.setSemester("2024-2025学年第一学期");
        period2.setStart_time("2024-12-01 00:00:00");
        period2.setEnd_time("2024-12-15 23:59:59");
        period2.setStatus(2); // 已结束
        
        periods.add(period1);
        periods.add(period2);
        
        PageResult<EvaluationPeriodDTO> pageResult = new PageResult<>();
        pageResult.setList(periods);
        pageResult.setTotal(2L);
        
        // 配置Mock行为
        when(evaluationPeriodService.getPeriodList(any())).thenReturn(pageResult);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评教周期列表成功"))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list.length()").value(2))
                .andExpect(jsonPath("$.data.total").value(2))
                .andExpect(jsonPath("$.data.list[0].period_id").value(1))
                .andExpect(jsonPath("$.data.list[0].semester").value("2024-2025学年第二学期"))
                .andExpect(jsonPath("$.data.list[0].status").value(1))
                .andExpect(jsonPath("$.data.list[1].period_id").value(2))
                .andExpect(jsonPath("$.data.list[1].semester").value("2024-2025学年第一学期"))
                .andExpect(jsonPath("$.data.list[1].status").value(2));
    }

    @Test
    public void testGetPeriodList_WithSemesterSearch() throws Exception {
        // 准备测试数据 - 搜索结果
        List<EvaluationPeriodDTO> periods = new ArrayList<>();
        
        EvaluationPeriodDTO period = new EvaluationPeriodDTO();
        period.setPeriod_id(1L);
        period.setSemester("2024-2025学年第二学期");
        period.setStart_time("2025-05-20 00:00:00");
        period.setEnd_time("2025-06-01 23:59:59");
        period.setStatus(1);
        
        periods.add(period);
        
        PageResult<EvaluationPeriodDTO> pageResult = new PageResult<>();
        pageResult.setList(periods);
        pageResult.setTotal(1L);
        
        // 配置Mock行为
        when(evaluationPeriodService.getPeriodList(any())).thenReturn(pageResult);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods")
                .param("page", "1")
                .param("limit", "10")
                .param("semester", "2024-2025"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list.length()").value(1))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].semester").value("2024-2025学年第二学期"));
    }

    @Test
    public void testGetPeriodList_WhenNoData() throws Exception {
        // 准备测试数据 - 没有数据
        PageResult<EvaluationPeriodDTO> pageResult = new PageResult<>();
        pageResult.setList(new ArrayList<>());
        pageResult.setTotal(0L);
        
        // 配置Mock行为
        when(evaluationPeriodService.getPeriodList(any())).thenReturn(pageResult);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list.length()").value(0))
                .andExpect(jsonPath("$.data.total").value(0));
    }

    @Test
    public void testGetPeriodList_WithPagination() throws Exception {
        // 准备测试数据 - 分页测试
        List<EvaluationPeriodDTO> periods = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            EvaluationPeriodDTO period = new EvaluationPeriodDTO();
            period.setPeriod_id((long) i);
            period.setSemester("学期" + i);
            period.setStart_time("2025-05-" + (19 + i) + " 00:00:00");
            period.setEnd_time("2025-06-" + (i) + " 23:59:59");
            period.setStatus(i % 3); // 0, 1, 2 循环
            periods.add(period);
        }
        
        PageResult<EvaluationPeriodDTO> pageResult = new PageResult<>();
        pageResult.setList(periods);
        pageResult.setTotal(15L); // 假设总共15条记录
        
        // 配置Mock行为
        when(evaluationPeriodService.getPeriodList(any())).thenReturn(pageResult);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods")
                .param("page", "2")
                .param("limit", "5"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.list.length()").value(5))
                .andExpect(jsonPath("$.data.total").value(15));
    }

    @Test
    public void testGetPeriodList_WhenException() throws Exception {
        // 配置Mock行为 - 抛出异常
        when(evaluationPeriodService.getPeriodList(any()))
                .thenThrow(new RuntimeException("数据库查询失败"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods")
                .param("page", "1")
                .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取评教周期列表失败: 数据库查询失败"));
    }

    @Test
    public void testGetPeriodList_WithDefaultParameters() throws Exception {
        // 准备测试数据
        PageResult<EvaluationPeriodDTO> pageResult = new PageResult<>();
        pageResult.setList(new ArrayList<>());
        pageResult.setTotal(0L);
        
        // 配置Mock行为
        when(evaluationPeriodService.getPeriodList(any())).thenReturn(pageResult);
        
        // 执行请求并验证结果 - 不传递参数，使用默认值
        mockMvc.perform(get("/evaluation/periods"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
