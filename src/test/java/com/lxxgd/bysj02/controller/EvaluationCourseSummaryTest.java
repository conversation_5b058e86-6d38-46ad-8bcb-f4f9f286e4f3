package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.dto.TeacherCourseDTO;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class EvaluationCourseSummaryTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationTaskService evaluationTaskService;

    @InjectMocks
    private EvaluationController evaluationController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(evaluationController)
                .build();
    }

    @Test
    public void testGetTeacherCourses_WhenHasCourses() throws Exception {
        // 准备测试数据
        List<TeacherCourseDTO> courses = new ArrayList<>();
        
        TeacherCourseDTO course1 = new TeacherCourseDTO();
        course1.setCourse_id(1);
        course1.setCourse_name("高等数学");
        course1.setCourse_code("MATH001");
        
        TeacherCourseDTO course2 = new TeacherCourseDTO();
        course2.setCourse_id(2);
        course2.setCourse_name("数据结构");
        course2.setCourse_code("CS002");
        
        courses.add(course1);
        courses.add(course2);
        
        // 配置Mock行为
        when(evaluationTaskService.getTeacherCoursesByPeriod(anyLong(), anyLong())).thenReturn(courses);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/course-summary")
                .param("period_id", "1")
                .param("userId", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取教师课程列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].course_id").value(1))
                .andExpect(jsonPath("$.data[0].course_name").value("高等数学"))
                .andExpect(jsonPath("$.data[0].course_code").value("MATH001"))
                .andExpect(jsonPath("$.data[1].course_id").value(2))
                .andExpect(jsonPath("$.data[1].course_name").value("数据结构"))
                .andExpect(jsonPath("$.data[1].course_code").value("CS002"));
    }

    @Test
    public void testGetTeacherCourses_WhenNoCourses() throws Exception {
        // 配置Mock行为 - 没有课程
        when(evaluationTaskService.getTeacherCoursesByPeriod(anyLong(), anyLong())).thenReturn(new ArrayList<>());
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/course-summary")
                .param("period_id", "1")
                .param("userId", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取教师课程列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    public void testGetTeacherCourses_WhenException() throws Exception {
        // 配置Mock行为 - 抛出异常
        when(evaluationTaskService.getTeacherCoursesByPeriod(anyLong(), anyLong())).thenThrow(new RuntimeException("数据库查询失败"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/course-summary")
                .param("period_id", "1")
                .param("userId", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取教师课程列表失败: 数据库查询失败"));
    }

    @Test
    public void testGetTeacherCourses_WhenMissingParameters() throws Exception {
        // 执行请求并验证结果 - 缺少必需参数
        mockMvc.perform(get("/evaluation/course-summary")
                .param("period_id", "1"))
                // 缺少userId参数
                .andExpect(status().isBadRequest());
    }
}
