package com.lxxgd.bysj02.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lxxgd.bysj02.dto.EvaluationDimensionTransferDTO;
import com.lxxgd.bysj02.service.IEvaluationDimensionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class EvaluationDimensionTransferTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationDimensionService evaluationDimensionService;

    @InjectMocks
    private EvaluationDimensionController evaluationDimensionController;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(evaluationDimensionController)
                .build();
    }

    @Test
    public void testGetEnabledDimensions() throws Exception {
        // 准备测试数据
        List<EvaluationDimensionTransferDTO> dimensions = new ArrayList<>();
        
        EvaluationDimensionTransferDTO dimension1 = new EvaluationDimensionTransferDTO();
        dimension1.setDimension_id(1L);
        dimension1.setDimension_name("教学态度");
        dimension1.setDescription("教师的教学态度和责任心");
        dimension1.setStatus((byte) 1);
        dimensions.add(dimension1);
        
        EvaluationDimensionTransferDTO dimension2 = new EvaluationDimensionTransferDTO();
        dimension2.setDimension_id(2L);
        dimension2.setDimension_name("教学内容");
        dimension2.setDescription("教学内容的组织和安排");
        dimension2.setStatus((byte) 1);
        dimensions.add(dimension2);
        
        // 配置Mock行为
        when(evaluationDimensionService.getEnabledDimensions(any())).thenReturn(dimensions);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation-dimensions")
                        .param("status", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评价维度列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].dimension_id").value(1))
                .andExpect(jsonPath("$.data[0].dimension_name").value("教学态度"))
                .andExpect(jsonPath("$.data[1].dimension_id").value(2))
                .andExpect(jsonPath("$.data[1].dimension_name").value("教学内容"));
    }
    
    @Test
    public void testGetEnabledDimensionsWithoutStatus() throws Exception {
        // 准备测试数据
        List<EvaluationDimensionTransferDTO> dimensions = new ArrayList<>();
        
        EvaluationDimensionTransferDTO dimension1 = new EvaluationDimensionTransferDTO();
        dimension1.setDimension_id(1L);
        dimension1.setDimension_name("教学态度");
        dimension1.setStatus((byte) 1);
        dimensions.add(dimension1);
        
        EvaluationDimensionTransferDTO dimension2 = new EvaluationDimensionTransferDTO();
        dimension2.setDimension_id(2L);
        dimension2.setDimension_name("教学内容");
        dimension2.setStatus((byte) 0);
        dimensions.add(dimension2);
        
        // 配置Mock行为
        when(evaluationDimensionService.getEnabledDimensions(null)).thenReturn(dimensions);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation-dimensions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));
    }
}
