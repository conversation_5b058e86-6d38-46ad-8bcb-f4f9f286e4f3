package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.service.IEvaluationTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class StudentControllerTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationTaskService evaluationTaskService;

    @InjectMocks
    private StudentController studentController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(studentController)
                .build();
    }

    @Test
    public void testGetEvaluationTaskStatus_WhenHasTask() throws Exception {
        // 准备测试数据
        Long studentId = 1L;
        
        // 配置Mock行为 - 学生有评教任务
        when(evaluationTaskService.hasCurrentEvaluationTask(anyLong())).thenReturn(true);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", studentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true))
                .andExpect(jsonPath("$.message").value("学生当前有评教任务"));
    }

    @Test
    public void testGetEvaluationTaskStatus_WhenNoTask() throws Exception {
        // 准备测试数据
        Long studentId = 2L;
        
        // 配置Mock行为 - 学生没有评教任务
        when(evaluationTaskService.hasCurrentEvaluationTask(anyLong())).thenReturn(false);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", studentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(false))
                .andExpect(jsonPath("$.message").value("学生当前没有评教任务"));
    }

    @Test
    public void testGetEvaluationTaskStatus_WhenException() throws Exception {
        // 准备测试数据
        Long studentId = 3L;
        
        // 配置Mock行为 - 抛出异常
        when(evaluationTaskService.hasCurrentEvaluationTask(anyLong())).thenThrow(new RuntimeException("测试异常"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/task-status/{studentId}", studentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取学生评教任务状态失败: 测试异常"));
    }
}
