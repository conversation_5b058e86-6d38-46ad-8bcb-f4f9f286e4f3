package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.dto.CourseSummaryDataDTO;
import com.lxxgd.bysj02.service.ICourseEvaluationSummaryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class CourseEvaluationSummaryTest {

    private MockMvc mockMvc;

    @Mock
    private ICourseEvaluationSummaryService courseEvaluationSummaryService;

    @InjectMocks
    private EvaluationController evaluationController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(evaluationController)
                .build();
    }

    @Test
    public void testGetCourseEvaluationSummary_WhenDataExists() throws Exception {
        // 准备测试数据
        CourseSummaryDataDTO summaryData = new CourseSummaryDataDTO();
        summaryData.setSummary_id(1);
        summaryData.setPeriod_id(1);
        summaryData.setCourse_id(1);
        summaryData.setTotal_score(new BigDecimal("85.5"));
        summaryData.setTotal_count(25);
        summaryData.setRanking("3");
        summaryData.setCreate_time("2025-05-20 10:00:00");
        summaryData.setUpdate_time("2025-05-26 15:30:00");
        
        // 配置Mock行为
        when(courseEvaluationSummaryService.getCourseEvaluationSummary(anyLong(), anyLong(), anyLong()))
                .thenReturn(summaryData);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/teacher/evaluation/course-summary")
                .param("userId", "2")
                .param("period_id", "1")
                .param("course_id", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取课程评价汇总数据成功"))
                .andExpect(jsonPath("$.data.summary_id").value(1))
                .andExpect(jsonPath("$.data.period_id").value(1))
                .andExpect(jsonPath("$.data.course_id").value(1))
                .andExpect(jsonPath("$.data.total_score").value(85.5))
                .andExpect(jsonPath("$.data.total_count").value(25))
                .andExpect(jsonPath("$.data.ranking").value("3"))
                .andExpect(jsonPath("$.data.create_time").value("2025-05-20 10:00:00"))
                .andExpect(jsonPath("$.data.update_time").value("2025-05-26 15:30:00"));
    }

    @Test
    public void testGetCourseEvaluationSummary_WhenNoData() throws Exception {
        // 准备测试数据 - 没有汇总数据
        CourseSummaryDataDTO summaryData = new CourseSummaryDataDTO();
        summaryData.setPeriod_id(1);
        summaryData.setCourse_id(1);
        summaryData.setTotal_score(BigDecimal.ZERO);
        summaryData.setTotal_count(0);
        summaryData.setRanking(null);
        
        // 配置Mock行为
        when(courseEvaluationSummaryService.getCourseEvaluationSummary(anyLong(), anyLong(), anyLong()))
                .thenReturn(summaryData);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/teacher/evaluation/course-summary")
                .param("userId", "2")
                .param("period_id", "1")
                .param("course_id", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取课程评价汇总数据成功"))
                .andExpect(jsonPath("$.data.period_id").value(1))
                .andExpect(jsonPath("$.data.course_id").value(1))
                .andExpect(jsonPath("$.data.total_score").value(0))
                .andExpect(jsonPath("$.data.total_count").value(0))
                .andExpect(jsonPath("$.data.ranking").isEmpty());
    }

    @Test
    public void testGetCourseEvaluationSummary_WhenException() throws Exception {
        // 配置Mock行为 - 抛出异常
        when(courseEvaluationSummaryService.getCourseEvaluationSummary(anyLong(), anyLong(), anyLong()))
                .thenThrow(new RuntimeException("数据库查询失败"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/teacher/evaluation/course-summary")
                .param("userId", "2")
                .param("period_id", "1")
                .param("course_id", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取课程评价汇总数据失败: 数据库查询失败"));
    }

    @Test
    public void testGetCourseEvaluationSummary_WhenMissingParameters() throws Exception {
        // 执行请求并验证结果 - 缺少必需参数
        mockMvc.perform(get("/teacher/evaluation/course-summary")
                .param("userId", "2")
                .param("period_id", "1"))
                // 缺少course_id参数
                .andExpect(status().isBadRequest());
    }
}
