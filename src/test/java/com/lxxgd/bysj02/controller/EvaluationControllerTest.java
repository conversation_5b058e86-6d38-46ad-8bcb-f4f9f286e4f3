package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.dto.EvaluationPeriodListDTO;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class EvaluationControllerTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationPeriodService evaluationPeriodService;

    @InjectMocks
    private EvaluationController evaluationController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(evaluationController)
                .build();
    }

    @Test
    public void testGetAllPeriods_WhenHasPeriods() throws Exception {
        // 准备测试数据
        List<EvaluationPeriodListDTO> periods = new ArrayList<>();
        
        EvaluationPeriodListDTO period1 = new EvaluationPeriodListDTO();
        period1.setId(1);
        period1.setName("2024春季学期");
        period1.setStart_time("2024-03-01 00:00:00");
        period1.setEnd_time("2024-03-15 23:59:59");
        period1.setStatus(1);
        
        EvaluationPeriodListDTO period2 = new EvaluationPeriodListDTO();
        period2.setId(2);
        period2.setName("2024秋季学期");
        period2.setStart_time("2024-09-01 00:00:00");
        period2.setEnd_time("2024-09-15 23:59:59");
        period2.setStatus(0);
        
        periods.add(period1);
        periods.add(period2);
        
        // 配置Mock行为
        when(evaluationPeriodService.getAllPeriods()).thenReturn(periods);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评教周期列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("2024春季学期"))
                .andExpect(jsonPath("$.data[0].start_time").value("2024-03-01 00:00:00"))
                .andExpect(jsonPath("$.data[0].end_time").value("2024-03-15 23:59:59"))
                .andExpect(jsonPath("$.data[0].status").value(1))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].name").value("2024秋季学期"))
                .andExpect(jsonPath("$.data[1].status").value(0));
    }

    @Test
    public void testGetAllPeriods_WhenNoPeriods() throws Exception {
        // 配置Mock行为 - 没有评教周期
        when(evaluationPeriodService.getAllPeriods()).thenReturn(new ArrayList<>());
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取评教周期列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    public void testGetAllPeriods_WhenException() throws Exception {
        // 配置Mock行为 - 抛出异常
        when(evaluationPeriodService.getAllPeriods()).thenThrow(new RuntimeException("数据库连接失败"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/evaluation/periods"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取评教周期列表失败: 数据库连接失败"));
    }
}
