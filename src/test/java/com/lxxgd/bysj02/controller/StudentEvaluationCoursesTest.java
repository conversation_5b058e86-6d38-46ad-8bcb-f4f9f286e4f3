package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.dto.CourseInfoDTO;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
public class StudentEvaluationCoursesTest {

    private MockMvc mockMvc;

    @Mock
    private IEvaluationTaskService evaluationTaskService;

    @InjectMocks
    private StudentController studentController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(studentController)
                .build();
    }

    @Test
    public void testGetEvaluationCourses_WhenHasCourses() throws Exception {
        // 准备测试数据
        Long studentId = 1L;
        Long teacherId = 2L;
        List<CourseInfoDTO> courses = new ArrayList<>();
        
        CourseInfoDTO course1 = new CourseInfoDTO();
        course1.setCourseId("1");
        course1.setCourseName("高等数学");
        course1.setCourseType("通识课");
        
        CourseInfoDTO course2 = new CourseInfoDTO();
        course2.setCourseId("2");
        course2.setCourseName("数据结构");
        course2.setCourseType("专业课");
        
        courses.add(course1);
        courses.add(course2);
        
        // 配置Mock行为 - 学生有可评价课程
        when(evaluationTaskService.getEvaluationCoursesByStudentAndTeacher(anyLong(), anyLong())).thenReturn(courses);
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/courses")
                .param("studentId", studentId.toString())
                .param("teacherId", teacherId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取可评价课程列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].courseId").value("1"))
                .andExpect(jsonPath("$.data[0].courseName").value("高等数学"))
                .andExpect(jsonPath("$.data[0].courseType").value("通识课"))
                .andExpect(jsonPath("$.data[1].courseId").value("2"))
                .andExpect(jsonPath("$.data[1].courseName").value("数据结构"))
                .andExpect(jsonPath("$.data[1].courseType").value("专业课"));
    }

    @Test
    public void testGetEvaluationCourses_WhenNoCourses() throws Exception {
        // 准备测试数据
        Long studentId = 1L;
        Long teacherId = 2L;
        
        // 配置Mock行为 - 学生没有可评价课程
        when(evaluationTaskService.getEvaluationCoursesByStudentAndTeacher(anyLong(), anyLong())).thenReturn(new ArrayList<>());
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/courses")
                .param("studentId", studentId.toString())
                .param("teacherId", teacherId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取可评价课程列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    public void testGetEvaluationCourses_WhenException() throws Exception {
        // 准备测试数据
        Long studentId = 1L;
        Long teacherId = 2L;
        
        // 配置Mock行为 - 抛出异常
        when(evaluationTaskService.getEvaluationCoursesByStudentAndTeacher(anyLong(), anyLong())).thenThrow(new RuntimeException("测试异常"));
        
        // 执行请求并验证结果
        mockMvc.perform(get("/student/evaluation/courses")
                .param("studentId", studentId.toString())
                .param("teacherId", teacherId.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取可评价课程列表失败: 测试异常"));
    }
}
