package com.lxxgd.bysj02.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lxxgd.bysj02.entity.EvaluationPeriod;
import com.lxxgd.bysj02.entity.EvaluationTask;
import com.lxxgd.bysj02.mapper.EvaluationPeriodMapper;
import com.lxxgd.bysj02.mapper.EvaluationTaskMapper;
import com.lxxgd.bysj02.service.impl.EvaluationTaskServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EvaluationTaskStatusServiceTest {

    @Mock
    private EvaluationTaskMapper evaluationTaskMapper;

    @Mock
    private EvaluationPeriodMapper evaluationPeriodMapper;

    @InjectMocks
    private EvaluationTaskServiceImpl evaluationTaskService;

    private Long studentId;
    private LocalDateTime now;

    @BeforeEach
    public void setup() {
        studentId = 1L;
        now = LocalDateTime.now();
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenNoTasksExist() {
        // 准备测试数据 - 没有评教任务
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(new ArrayList<>());
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertFalse(result, "当没有评教任务时，应该返回false");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenTaskExistsButCompleted() {
        // 准备测试数据 - 有评教任务但已完成（status=1）
        EvaluationTask completedTask = new EvaluationTask();
        completedTask.setTaskId(1L);
        completedTask.setStudentId(studentId);
        completedTask.setPeriodId(1L);
        completedTask.setStatus((byte) 1); // 已完成
        
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(new ArrayList<>()); // 查询status=0的任务时返回空列表
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertFalse(result, "当评教任务已完成时，应该返回false");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenTaskExistsButPeriodNotStarted() {
        // 准备测试数据 - 有未完成的评教任务但评教周期未开始
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setStudentId(studentId);
        task.setPeriodId(1L);
        task.setStatus((byte) 0); // 未完成
        
        EvaluationPeriod period = new EvaluationPeriod();
        period.setPeriodId(1L);
        period.setStartTime(now.plusDays(1)); // 明天开始
        period.setEndTime(now.plusDays(7));   // 一周后结束
        
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(task));
        when(evaluationPeriodMapper.selectById(1L)).thenReturn(period);
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertFalse(result, "当评教周期未开始时，应该返回false");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenTaskExistsButPeriodEnded() {
        // 准备测试数据 - 有未完成的评教任务但评教周期已结束
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setStudentId(studentId);
        task.setPeriodId(1L);
        task.setStatus((byte) 0); // 未完成
        
        EvaluationPeriod period = new EvaluationPeriod();
        period.setPeriodId(1L);
        period.setStartTime(now.minusDays(7)); // 一周前开始
        period.setEndTime(now.minusDays(1));   // 昨天结束
        
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(task));
        when(evaluationPeriodMapper.selectById(1L)).thenReturn(period);
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertFalse(result, "当评教周期已结束时，应该返回false");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenTaskExistsAndPeriodActive() {
        // 准备测试数据 - 有未完成的评教任务且评教周期正在进行
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setStudentId(studentId);
        task.setPeriodId(1L);
        task.setStatus((byte) 0); // 未完成
        
        EvaluationPeriod period = new EvaluationPeriod();
        period.setPeriodId(1L);
        period.setStartTime(now.minusDays(1)); // 昨天开始
        period.setEndTime(now.plusDays(7));    // 一周后结束
        
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(task));
        when(evaluationPeriodMapper.selectById(1L)).thenReturn(period);
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertTrue(result, "当有未完成的评教任务且评教周期正在进行时，应该返回true");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenTaskExistsAndPeriodJustStarted() {
        // 准备测试数据 - 评教周期刚好开始
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setStudentId(studentId);
        task.setPeriodId(1L);
        task.setStatus((byte) 0); // 未完成
        
        EvaluationPeriod period = new EvaluationPeriod();
        period.setPeriodId(1L);
        period.setStartTime(now); // 现在开始
        period.setEndTime(now.plusDays(7)); // 一周后结束
        
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(task));
        when(evaluationPeriodMapper.selectById(1L)).thenReturn(period);
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertTrue(result, "当评教周期刚好开始时，应该返回true");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenTaskExistsAndPeriodJustEnded() {
        // 准备测试数据 - 评教周期刚好结束
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setStudentId(studentId);
        task.setPeriodId(1L);
        task.setStatus((byte) 0); // 未完成
        
        EvaluationPeriod period = new EvaluationPeriod();
        period.setPeriodId(1L);
        period.setStartTime(now.minusDays(7)); // 一周前开始
        period.setEndTime(now); // 现在结束
        
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(task));
        when(evaluationPeriodMapper.selectById(1L)).thenReturn(period);
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertTrue(result, "当评教周期刚好结束时，应该返回true");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenMultipleTasksWithMixedStatus() {
        // 准备测试数据 - 多个评教任务，有的已完成，有的未完成
        EvaluationTask uncompletedTask = new EvaluationTask();
        uncompletedTask.setTaskId(1L);
        uncompletedTask.setStudentId(studentId);
        uncompletedTask.setPeriodId(1L);
        uncompletedTask.setStatus((byte) 0); // 未完成
        
        EvaluationPeriod activePeriod = new EvaluationPeriod();
        activePeriod.setPeriodId(1L);
        activePeriod.setStartTime(now.minusDays(1)); // 昨天开始
        activePeriod.setEndTime(now.plusDays(7));    // 一周后结束
        
        // 只返回未完成的任务（因为查询条件包含status=0）
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(uncompletedTask));
        when(evaluationPeriodMapper.selectById(1L)).thenReturn(activePeriod);
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertTrue(result, "当有未完成的评教任务且在有效周期内时，应该返回true");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenPeriodNotFound() {
        // 准备测试数据 - 有评教任务但找不到对应的评教周期
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setStudentId(studentId);
        task.setPeriodId(1L);
        task.setStatus((byte) 0); // 未完成
        
        when(evaluationTaskMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(task));
        when(evaluationPeriodMapper.selectById(1L)).thenReturn(null); // 找不到周期
        
        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(studentId);
        
        // 验证结果
        assertFalse(result, "当找不到对应的评教周期时，应该返回false");
    }
}
