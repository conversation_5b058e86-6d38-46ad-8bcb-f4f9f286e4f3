package com.lxxgd.bysj02.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lxxgd.bysj02.entity.EvaluationPeriod;
import com.lxxgd.bysj02.entity.EvaluationTask;
import com.lxxgd.bysj02.mapper.EvaluationPeriodMapper;
import com.lxxgd.bysj02.mapper.EvaluationTaskMapper;
import com.lxxgd.bysj02.service.impl.EvaluationTaskServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class EvaluationTaskServiceTest {

    @Mock
    private EvaluationTaskMapper evaluationTaskMapper;

    @Mock
    private EvaluationPeriodMapper evaluationPeriodMapper;

    @Spy
    @InjectMocks
    private EvaluationTaskServiceImpl evaluationTaskService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenNoTasks() {
        // 配置Mock行为 - 没有评教任务
        doReturn(Collections.emptyList()).when(evaluationTaskService).list(any(LambdaQueryWrapper.class));

        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(1L);

        // 验证结果
        assertFalse(result, "学生没有评教任务时应返回false");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenHasTaskButNotInPeriod() {
        // 准备测试数据
        List<EvaluationTask> tasks = new ArrayList<>();
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setPeriodId(1L);
        task.setStudentId(1L);
        tasks.add(task);

        // 配置Mock行为 - 有评教任务但不在评教周期内
        doReturn(tasks).when(evaluationTaskService).list(any(LambdaQueryWrapper.class));

        // 创建一个已结束的评教周期
        EvaluationPeriod period = new EvaluationPeriod();
        period.setPeriodId(1L);
        period.setStartTime(LocalDateTime.now().minusDays(30)); // 30天前开始
        period.setEndTime(LocalDateTime.now().minusDays(15));   // 15天前结束
        when(evaluationPeriodMapper.selectById(anyLong())).thenReturn(period);

        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(1L);

        // 验证结果
        assertFalse(result, "评教任务不在有效周期内时应返回false");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenHasTaskInPeriod() {
        // 准备测试数据
        List<EvaluationTask> tasks = new ArrayList<>();
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setPeriodId(1L);
        task.setStudentId(1L);
        tasks.add(task);

        // 配置Mock行为 - 有评教任务且在评教周期内
        doReturn(tasks).when(evaluationTaskService).list(any(LambdaQueryWrapper.class));

        // 创建一个正在进行的评教周期
        EvaluationPeriod period = new EvaluationPeriod();
        period.setPeriodId(1L);
        period.setStartTime(LocalDateTime.now().minusDays(5)); // 5天前开始
        period.setEndTime(LocalDateTime.now().plusDays(5));    // 5天后结束
        when(evaluationPeriodMapper.selectById(anyLong())).thenReturn(period);

        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(1L);

        // 验证结果
        assertTrue(result, "评教任务在有效周期内时应返回true");
    }

    @Test
    public void testHasCurrentEvaluationTask_WhenPeriodNotFound() {
        // 准备测试数据
        List<EvaluationTask> tasks = new ArrayList<>();
        EvaluationTask task = new EvaluationTask();
        task.setTaskId(1L);
        task.setPeriodId(1L);
        task.setStudentId(1L);
        tasks.add(task);

        // 配置Mock行为 - 有评教任务但评教周期不存在
        doReturn(tasks).when(evaluationTaskService).list(any(LambdaQueryWrapper.class));
        when(evaluationPeriodMapper.selectById(anyLong())).thenReturn(null);

        // 执行测试
        boolean result = evaluationTaskService.hasCurrentEvaluationTask(1L);

        // 验证结果
        assertFalse(result, "评教周期不存在时应返回false");
    }
}
