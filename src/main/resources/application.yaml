spring:
  application:
    name: bysj02
#  autoconfigure:
#    exclude: org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration

  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ****************************************************************************************************************
#    username: root
#    password: 123456
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**************:16094/gd?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&allowMultiQueries=true
    username: root
    password: 123456
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

mybatis-plus:
  configuration:
    # ??SQL????
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # ????????
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      # ??????
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  # mapper.xml????
  mapper-locations: classpath*:/mapper/**/*.xml
  # ??????
  type-aliases-package: com.lxxgd.bysj02.entity

server:
  port: 28080

jwt:
  # secret????Base64??
  secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  expiration: 3600000  # 1??
  refresh-expiration: 86400000  # 24??

