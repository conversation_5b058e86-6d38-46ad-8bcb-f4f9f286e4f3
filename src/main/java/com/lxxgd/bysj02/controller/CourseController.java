package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.CourseCascaderDTO;
import com.lxxgd.bysj02.service.ICourseService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 课程表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-23
 */
@RestController
@RequestMapping
@RequiredArgsConstructor
public class CourseController {

    @Resource
    private ICourseService courseService;

    @GetMapping("/course-cascader")
    public Result<List<CourseCascaderDTO>> getTeachingCourses() {
        return Result.success(courseService.getCascaderList(), "获取获取课程列表成功");
    }

//    @GetMapping("/courses/cascade/list")
//    public Result<List<CourseDTO>> getTeachingCourses() {
//        return Result.success(courseService.getList(), "获取获取课程列表成功");
//    }

//    @GetMapping("/departments")
//    public Result<List<CourseDTO>> getDepartments() {
//        return Result.success(courseService.getDepartments(), "获取院系列表成功");
//    }

//    @GetMapping("/courses/types")
//    public Result<List<CourseTypeDTO>> getCourseTypes(@RequestParam Long departmentId) {
//        return Result.success(courseService.getCourseTypesByDepartmentId(departmentId), "获取课程类型列表成功");
//    }

}
