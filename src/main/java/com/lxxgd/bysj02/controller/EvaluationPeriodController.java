package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.exception.CustomException;
import com.lxxgd.bysj02.common.result.PageResult;
import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.EvaluationPeriodDTO;
import com.lxxgd.bysj02.dto.PeriodFormDataDTO;
import com.lxxgd.bysj02.dto.PeriodListParamsDTO;
import com.lxxgd.bysj02.dto.PublishTaskResultDTO;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 评教周期表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@RestController
@Tag(name = "评教周期管理", description = "评教周期相关接口")
@Slf4j
public class EvaluationPeriodController {

    @Resource
    private IEvaluationPeriodService evaluationPeriodService;

    /**
     * 获取评教周期列表（分页 + 搜索）
     * @param params 查询参数
     * @return 分页结果
     */
    @GetMapping("/evaluation/periods")
    @Operation(summary = "获取评教周期列表", description = "支持分页和学期名称筛选")
    public Result<PageResult<EvaluationPeriodDTO>> getPeriodList(PeriodListParamsDTO params) {
        try {
            PageResult<EvaluationPeriodDTO> pageResult = evaluationPeriodService.getPeriodList(params);
            return Result.success(pageResult, "获取评教周期列表成功");
        } catch (Exception e) {
            log.error("获取评教周期列表失败", e);
            return Result.fail("获取评教周期列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建新的评教周期
     * @param periodFormDataDTO 评教周期表单数据
     * @return 创建后的评教周期
     */
    @PostMapping("/evaluation/periods")
    @Operation(summary = "创建新的评教周期", description = "创建新的评教周期")
    public Result<EvaluationPeriodDTO> createPeriod(@RequestBody @Valid PeriodFormDataDTO periodFormDataDTO) {
        try {
            EvaluationPeriodDTO createdPeriod = evaluationPeriodService.createPeriod(periodFormDataDTO);
            return Result.success(createdPeriod, "创建评教周期成功");
        } catch (CustomException e) {
            log.error("创建评教周期失败: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("创建评教周期异常", e);
            return Result.fail("创建评教周期失败: " + e.getMessage());
        }
    }

    /**
     * 更新评教周期
     * @param periodId 评教周期ID
     * @param periodFormDataDTO 评教周期表单数据
     * @return 更新后的评教周期
     */
    @PutMapping("/evaluation/periods/{periodId}")
    @Operation(summary = "更新评教周期", description = "根据ID更新评教周期")
    public Result<EvaluationPeriodDTO> updatePeriod(
            @PathVariable Long periodId,
            @RequestBody @Valid PeriodFormDataDTO periodFormDataDTO) {
        try {
            EvaluationPeriodDTO updatedPeriod = evaluationPeriodService.updatePeriod(periodId, periodFormDataDTO);
            return Result.success(updatedPeriod, "更新评教周期成功");
        } catch (CustomException e) {
            log.error("更新评教周期失败: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("更新评教周期异常", e);
            return Result.fail("更新评教周期失败: " + e.getMessage());
        }
    }

    /**
     * 删除评教周期
     * @param periodId 评教周期ID
     * @return 删除结果
     */
    @DeleteMapping("/evaluation/periods/{periodId}")
    @Operation(summary = "删除评教周期", description = "根据ID删除评教周期")
    public Result<Void> deletePeriod(@PathVariable Long periodId) {
        try {
            boolean deleted = evaluationPeriodService.deletePeriod(periodId);
            if (deleted) {
                return Result.success(null, "删除评教周期成功");
            } else {
                return Result.fail("删除评教周期失败");
            }
        } catch (CustomException e) {
            log.error("删除评教周期失败: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("删除评教周期异常", e);
            return Result.fail("删除评教周期失败: " + e.getMessage());
        }
    }

    /**
     * 发布评教任务
     * @param periodId 评教周期ID
     * @return 发布结果
     */
    @PostMapping("/evaluation/periods/{periodId}/publish")
    @Operation(summary = "发布评教任务", description = "根据评教周期ID发布评教任务")
    public Result<PublishTaskResultDTO> publishTasks(@PathVariable Long periodId) {
        try {
            PublishTaskResultDTO result = evaluationPeriodService.publishTasks(periodId);
            return Result.success(result, "发布评教任务成功，共创建" + result.getTaskCount() + "个任务");
        } catch (CustomException e) {
            log.error("发布评教任务失败: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("发布评教任务异常", e);
            return Result.fail("发布评教任务失败: " + e.getMessage());
        }
    }
}
