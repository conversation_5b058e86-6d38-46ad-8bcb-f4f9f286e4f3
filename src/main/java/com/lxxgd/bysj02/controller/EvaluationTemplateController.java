package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.exception.CustomException;
import com.lxxgd.bysj02.common.result.PageResult;
import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.CreateTemplateDTO;
import com.lxxgd.bysj02.dto.EvaluationTemplateDTO;
import com.lxxgd.bysj02.dto.TemplateListParamsDTO;
import com.lxxgd.bysj02.dto.TemplateDimensionConfigItemDTO;
import com.lxxgd.bysj02.dto.TemplateDimensionDetailDTO;
import com.lxxgd.bysj02.dto.UpdateTemplateDTO;
import com.lxxgd.bysj02.service.IEvaluationTemplateService;
import com.lxxgd.bysj02.service.ITemplateDimensionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 评分模板表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@RestController
@Tag(name = "评价模板管理", description = "评价模板相关接口")
public class EvaluationTemplateController {

    @Resource
    private IEvaluationTemplateService evaluationTemplateService;

    @Resource
    private ITemplateDimensionService templateDimensionService;

    /**
     * 获取评价模板列表（分页 + 搜索）
     * @param params 查询参数
     * @return 分页结果
     */
    @GetMapping("/evaluation-templates")
    @Operation(summary = "获取评价模板列表", description = "支持分页和模板名称、状态筛选")
    public Result<PageResult<EvaluationTemplateDTO>> getTemplateList(TemplateListParamsDTO params) {
        PageResult<EvaluationTemplateDTO> pageResult = evaluationTemplateService.getTemplateList(params);
        return Result.success(pageResult, "获取评价模板列表成功");
    }

    /**
     * 创建评价模板
     * @param createTemplateDTO 创建评价模板的请求DTO
     * @return 创建成功后的评价模板DTO
     */
    @PostMapping("/evaluation-templates")
    @Operation(summary = "创建评价模板", description = "创建新的评价模板")
    public Result<EvaluationTemplateDTO> createTemplate(@RequestBody @Valid CreateTemplateDTO createTemplateDTO) {
        try {
            // 模拟当前登录用户ID，实际应从登录用户中获取
            Long currentUserId = 1L; // 测试用户ID，实际应从安全上下文中获取

            EvaluationTemplateDTO templateDTO = evaluationTemplateService.createTemplate(createTemplateDTO, currentUserId);
            return Result.success(templateDTO, "创建评价模板成功");
        } catch (CustomException e) {
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 更新评价模板
     * @param templateId 要更新的模板ID
     * @param updateTemplateDTO 更新评价模板的请求DTO
     * @return 更新成功后的评价模板DTO
     */
    @PutMapping("/evaluation-templates/{templateId}")
    @Operation(summary = "更新评价模板", description = "更新现有的评价模板")
    public Result<EvaluationTemplateDTO> updateTemplate(
            @PathVariable Long templateId,
            @RequestBody @Valid UpdateTemplateDTO updateTemplateDTO) {
        try {
            // 模拟当前登录用户ID，实际应从登录用户中获取
            Long currentUserId = 1L; // 测试用户ID，实际应从安全上下文中获取

            EvaluationTemplateDTO templateDTO = evaluationTemplateService.updateTemplate(templateId, updateTemplateDTO, currentUserId);
            return Result.success(templateDTO, "更新评价模板成功");
        } catch (CustomException e) {
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 获取指定模板已配置的维度列表（包含权重和排序）
     * @param templateId 模板ID
     * @return 模板关联的维度详情列表
     */
    @GetMapping("/evaluation-templates/{templateId}/dimensions")
    @Operation(summary = "获取模板维度列表", description = "获取指定模板已配置的维度列表，包含权重和排序信息")
    public Result<List<TemplateDimensionDetailDTO>> getTemplateDimensions(@PathVariable Long templateId) {
        try {
            List<TemplateDimensionDetailDTO> dimensions = templateDimensionService.getTemplateDimensions(templateId);
            return Result.success(dimensions, "获取模板维度列表成功");
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 保存（更新）指定模板的维度配置
     * @param templateId 模板ID
     * @param configItems 维度配置项列表
     * @return 是否更新成功
     */
    @PutMapping("/evaluation-templates/{templateId}/dimensions")
    @Operation(summary = "更新模板维度配置", description = "保存（更新）指定模板的维度配置，包括添加、更新、删除操作")
    public Result<Void> updateTemplateDimensions(
            @PathVariable Long templateId,
            @RequestBody @Valid List<TemplateDimensionConfigItemDTO> configItems) {
        try {
            boolean success = templateDimensionService.updateTemplateDimensions(templateId, configItems);
            if (success) {
                return Result.success(null, "更新模板维度配置成功");
            } else {
                return Result.fail("更新模板维度配置失败");
            }
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 删除评价模板
     * @param templateId 要删除的模板ID
     * @return 删除结果
     */
    @DeleteMapping("/evaluation-templates/{templateId}")
    @Operation(summary = "删除评价模板", description = "删除指定的评价模板")
    public Result<Void> deleteTemplate(@PathVariable Long templateId) {
        try {
            boolean success = evaluationTemplateService.deleteTemplate(templateId);
            if (success) {
                return Result.success(null, "删除评价模板成功");
            } else {
                return Result.fail("删除评价模板失败");
            }
        } catch (CustomException e) {
            return Result.fail(e.getMessage());
        }
    }
}
