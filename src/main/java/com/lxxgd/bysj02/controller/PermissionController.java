package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.entity.Permission;
import com.lxxgd.bysj02.service.IPermissionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 权限表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@RestController
@Slf4j
public class PermissionController {

    @Resource
    private IPermissionService permissionService;

    /**
     * 根据ID列表获取权限
     * @param ids 权限ID列表
     * @return 权限列表
     */
    @GetMapping("/permissions/by-ids")
    public Result<List<Permission>> getPermissionsByIds(@RequestParam List<Long> ids) {
        try {
            List<Permission> permissions = permissionService.listByIds(ids);
            return Result.success(permissions, "获取权限成功");
        } catch (Exception e) {
            log.error("获取权限时发生错误", e);
            return Result.fail("获取权限失败：" + e.getMessage());
        }
    }
}
