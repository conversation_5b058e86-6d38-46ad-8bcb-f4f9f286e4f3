package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.CourseInfoDTO;
import com.lxxgd.bysj02.dto.EvaluationDimensionDetailDTO;
import com.lxxgd.bysj02.dto.TeacherInfoDTO;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 学生信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@RestController
@RequestMapping("/student")
@Tag(name = "学生管理", description = "学生相关接口")
@Slf4j
public class StudentController {

    @Resource
    private IEvaluationTaskService evaluationTaskService;

    /**
     * 获取学生当前的评教任务状态
     * @param studentId 学生ID
     * @return 是否有当前评教任务
     */
    @GetMapping("/evaluation/task-status/{studentId}")
    @Operation(summary = "获取学生当前的评教任务状态", description = "检查学生是否有当前评教任务")
    public Result<Boolean> getEvaluationTaskStatus(@PathVariable Long studentId) {
        try {
            boolean hasTask = evaluationTaskService.hasCurrentEvaluationTask(studentId);
            return Result.success(hasTask, hasTask ? "学生当前有评教任务" : "学生当前没有评教任务");
        } catch (Exception e) {
            log.error("获取学生评教任务状态失败", e);
            return Result.fail("获取学生评教任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取学生待评价的教师列表
     * @param studentId 学生ID
     * @return 待评价教师列表
     */
    @GetMapping("/evaluation/teachers/{studentId}")
    @Operation(summary = "获取学生待评价的教师列表", description = "获取学生待评价的教师列表")
    public Result<List<TeacherInfoDTO>> getEvaluationTeachers(@PathVariable Long studentId) {
        try {
            List<TeacherInfoDTO> teachers = evaluationTaskService.getEvaluationTeachersByStudentId(studentId);
            return Result.success(teachers, "获取待评价教师列表成功");
        } catch (Exception e) {
            log.error("获取待评价教师列表失败", e);
            return Result.fail("获取待评价教师列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据教师ID和学生ID获取可评价课程列表
     * @param studentId 学生ID
     * @param teacherId 教师ID
     * @return 可评价课程列表
     */
    @GetMapping("/evaluation/courses")
    @Operation(summary = "获取可评价课程列表", description = "根据教师ID和学生ID获取可评价课程列表")
    public Result<List<CourseInfoDTO>> getEvaluationCourses(
            @RequestParam Long studentId,
            @RequestParam Long teacherId) {
        try {
            List<CourseInfoDTO> courses = evaluationTaskService.getEvaluationCoursesByStudentAndTeacher(studentId, teacherId);
            return Result.success(courses, "获取可评价课程列表成功");
        } catch (Exception e) {
            log.error("获取可评价课程列表失败", e);
            return Result.fail("获取可评价课程列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前生效的评价模板下的评价维度、指标和评分选项
     * @return 评价维度详情列表
     */
    @GetMapping("/evaluation/dimensions")
    @Operation(summary = "获取评价维度详情", description = "获取当前生效的评价模板下的评价维度、指标和评分选项")
    public Result<List<EvaluationDimensionDetailDTO>> getEvaluationDimensions() {
        try {
            List<EvaluationDimensionDetailDTO> dimensions = evaluationTaskService.getCurrentEvaluationDimensions();
            return Result.success(dimensions, "获取评价维度详情成功");
        } catch (Exception e) {
            log.error("获取评价维度详情失败", e);
            return Result.fail("获取评价维度详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据学生的选择（学生ID、教师ID、课程ID）获取评教任务的taskId
     * @param studentId 学生ID
     * @param teacherId 教师ID
     * @param courseId 课程ID
     * @return 评教任务ID
     */
    @GetMapping("/evaluation/task-id")
    @Operation(summary = "获取评教任务ID", description = "根据学生的选择（学生ID、教师ID、课程ID）获取评教任务的taskId")
    public Result<TaskIdResponse> getEvaluationTaskId(
            @RequestParam Long studentId,
            @RequestParam Long teacherId,
            @RequestParam Long courseId) {
        try {
            Long taskId = evaluationTaskService.getEvaluationTaskId(studentId, teacherId, courseId);
            TaskIdResponse response = new TaskIdResponse();
            response.setTaskId(taskId != null ? String.valueOf(taskId) : null);
            return Result.success(response, "获取评教任务ID成功");
        } catch (Exception e) {
            log.error("获取评教任务ID失败", e);
            return Result.fail("获取评教任务ID失败: " + e.getMessage());
        }
    }

    /**
     * 任务ID响应类
     */
    public static class TaskIdResponse {
        private String taskId;

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }
    }
}
