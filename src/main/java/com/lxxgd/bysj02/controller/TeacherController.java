package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.exception.CustomException;
import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.CourseDTO;
import com.lxxgd.bysj02.dto.TeacherCourseInfoDTO;
import com.lxxgd.bysj02.dto.TeacherDTO;
import com.lxxgd.bysj02.dto.UpdateTeacherInfoDTO;
import com.lxxgd.bysj02.service.ITeacherService;
import jakarta.annotation.Resource;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 教师信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@RestController
@RequestMapping
@Tag(name = "教师管理", description = "教师相关接口")
@Slf4j
public class TeacherController {

    @Resource
    private ITeacherService teacherService;

    @GetMapping("/teacher/get/{teacherId}")
    @Operation(summary = "获取教师信息", description = "根据教师ID获取教师信息")
    public Result<TeacherDTO> getTeacherById(@PathVariable Long teacherId) {
        TeacherDTO teacherDTO = teacherService.getTeacherById(teacherId);

        if (teacherDTO != null) {
            return Result.success(teacherDTO, "获取教师信息成功");
        } else {
            return Result.fail("获取教师信息失败");
        }
    }

    /**
     * 更新教师信息
     * @param teacherId 教师ID
     * @param updateTeacherInfoDTO 更新教师信息DTO
     * @return 更新后的教师信息
     */
    @PutMapping("/teacher/upload/{teacherId}")
    @Operation(summary = "更新教师信息", description = "根据教师ID更新教师信息")
    public Result<TeacherDTO> updateTeacherInfo(@PathVariable Long teacherId, @RequestBody @Valid UpdateTeacherInfoDTO updateTeacherInfoDTO) {
        try {
            // 确保路径参数和请求体中的ID一致
            if (!teacherId.equals(updateTeacherInfoDTO.getTeacherId())) {
                return Result.fail("路径参数与请求体中的教师ID不一致");
            }

            TeacherDTO updatedTeacher = teacherService.updateTeacherInfo(updateTeacherInfoDTO);
            return Result.success(updatedTeacher, "更新教师信息成功");
        } catch (CustomException e) {
            log.error("更新教师信息失败: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("更新教师信息异常", e);
            return Result.fail("更新教师信息失败: " + e.getMessage());
        }
    }

//    @GetMapping("/teachers/{teacherId}/courses")
//    public Result<List<TeacherCourseInfoDTO>> getCoursesByTeacherId(@PathVariable Long teacherId) {
//        List<TeacherCourseInfoDTO> teacherCourseInfoDTOList = teacherService.getCoursesByTeacherId(teacherId);
//
//        return Result.success(teacherCourseInfoDTOList, "获取教师课程成功");
//    }
}
