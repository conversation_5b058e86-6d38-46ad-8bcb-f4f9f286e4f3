package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.AddUserDTO;
import com.lxxgd.bysj02.service.IUserService;
import com.lxxgd.bysj02.vo.AccountUserVO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 账户相关 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/account")
@Slf4j
public class AccountController {

    @Resource
    private IUserService userService;

    /**
     * 获取用户列表
     * 通过角色id查询user表来获取用户列表，只获取教师、学生
     * @param roleId 角色ID
     * @return 用户列表
     */
    @GetMapping("/getLists")
    public Result<List<AccountUserVO>> getUserByRoleId(@RequestParam Long roleId) {
        List<AccountUserVO> users = userService.getUserByRoleId(roleId);
        if (users == null || users.isEmpty()) {
            return Result.fail("未找到指定角色的用户");
        }
        return Result.success(users, "获取用户列表成功");
    }

    /**
     * 添加用户
     * 用于账户管理页面的新增用户功能，需要后端更新user表以及相关的学生表和教师表
     * @param addUserDTO 添加用户DTO
     * @return 添加的用户信息
     */
    @PostMapping("/addUser")
    public Result<AccountUserVO> addUser(@RequestBody @Valid AddUserDTO addUserDTO) {
        try {
            AccountUserVO user = userService.addUser(addUserDTO);
            return Result.success(user, "添加用户成功");
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 删除用户
     * 批量删除用户，根据用户ID列表删除用户信息，包括用户表和相应的学生/教师表中的信息
     * @param userIds 用户ID列表
     * @return 删除结果
     */
    @PostMapping("/deleteUser")
    public Result<Void> deleteUser(@RequestBody @Valid List<Long> userIds) {
        try {
            log.info("收到删除用户请求，用户ID列表: {}", userIds);
            if (userIds == null || userIds.isEmpty()) {
                log.warn("用户ID列表为空");
                return Result.fail("用户ID列表不能为空");
            }
            boolean success = userService.deleteUsers(userIds);
            if (success) {
                log.info("删除用户成功，用户ID列表: {}", userIds);
                return Result.success(null, "删除用户成功");
            } else {
                log.warn("删除用户失败，用户ID列表: {}", userIds);
                return Result.fail("删除用户失败");
            }
        } catch (Exception e) {
            log.error("删除用户异常，用户ID列表: {}, 异常信息: {}", userIds, e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }
}
