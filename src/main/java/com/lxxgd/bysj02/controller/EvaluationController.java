package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.EvaluationPeriodListDTO;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 评教相关 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@RestController
@RequestMapping("/evaluation")
@Tag(name = "评教管理", description = "评教相关接口")
@Slf4j
public class EvaluationController {

    @Resource
    private IEvaluationPeriodService evaluationPeriodService;

    /**
     * 获取所有评教周期列表
     * @return 评教周期列表
     */
    @GetMapping("/periods")
    @Operation(summary = "获取评教周期列表", description = "获取所有评教周期列表")
    public Result<List<EvaluationPeriodListDTO>> getAllPeriods() {
        try {
            List<EvaluationPeriodListDTO> periods = evaluationPeriodService.getAllPeriods();
            return Result.success(periods, "获取评教周期列表成功");
        } catch (Exception e) {
            log.error("获取评教周期列表失败", e);
            return Result.fail("获取评教周期列表失败: " + e.getMessage());
        }
    }
}
