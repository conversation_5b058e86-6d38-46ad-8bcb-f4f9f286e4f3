package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.EvaluationPeriodListDTO;
import com.lxxgd.bysj02.dto.TeacherCourseDTO;
import com.lxxgd.bysj02.dto.TeacherCoursesParamsDTO;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 评教相关 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24
 */
@RestController
@RequestMapping("/teacher")
@Tag(name = "教师评教管理", description = "教师评教相关接口")
@Slf4j
public class EvaluationController {

    @Resource
    private IEvaluationPeriodService evaluationPeriodService;

    @Resource
    private IEvaluationTaskService evaluationTaskService;

    /**
     * 获取所有评教周期列表
     * @return 评教周期列表
     */
    @GetMapping("/evaluation/periods")
    @Operation(summary = "获取评教周期列表", description = "获取所有评教周期列表")
    public Result<List<EvaluationPeriodListDTO>> getAllPeriods() {
        try {
            List<EvaluationPeriodListDTO> periods = evaluationPeriodService.getAllPeriods();
            return Result.success(periods, "获取评教周期列表成功");
        } catch (Exception e) {
            log.error("获取评教周期列表失败", e);
            return Result.fail("获取评教周期列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据评教周期获取指定教师的课程列表
     * @param params 查询参数（包含period_id和userId）
     * @return 教师课程列表
     */
    @GetMapping("/evaluation/course-summary")
    @Operation(summary = "获取教师课程列表", description = "根据评教周期获取指定教师的课程列表")
    public Result<List<TeacherCourseDTO>> getTeacherCourses(@Valid TeacherCoursesParamsDTO params) {
        try {
            Long periodId = params.getPeriod_id().longValue();
            Long teacherId = params.getUserId().longValue();

            List<TeacherCourseDTO> courses = evaluationTaskService.getTeacherCoursesByPeriod(periodId, teacherId);
            return Result.success(courses, "获取教师课程列表成功");
        } catch (Exception e) {
            log.error("获取教师课程列表失败", e);
            return Result.fail("获取教师课程列表失败: " + e.getMessage());
        }
    }
}
