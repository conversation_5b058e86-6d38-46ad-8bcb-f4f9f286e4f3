package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.entity.Semester;
import com.lxxgd.bysj02.service.ISemesterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 学期表 (简化版) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@RestController
@RequestMapping("/semester")
@Tag(name = "学期管理", description = "学期相关接口")
@Slf4j
public class SemesterController {

    @Resource
    private ISemesterService semesterService;

    /**
     * 获取当前学期信息（状态为2-进行中的学期）
     * @return 当前学期名称
     */
    @GetMapping("/current")
    @Operation(summary = "获取当前学期信息", description = "获取当前学期信息（状态为2-进行中的学期）")
    public Result<String> getCurrentSemester() {
        try {
            Semester currentSemester = semesterService.getCurrentSemester();
            if (currentSemester != null) {
                return Result.success(currentSemester.getSemesterName(), "获取当前学期信息成功");
            } else {
                return Result.success(null, "当前没有进行中的学期");
            }
        } catch (Exception e) {
            log.error("获取当前学期信息失败", e);
            return Result.fail("获取当前学期信息失败: " + e.getMessage());
        }
    }
}
