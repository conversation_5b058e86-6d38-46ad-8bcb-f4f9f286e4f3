package com.lxxgd.bysj02.controller;

import com.lxxgd.bysj02.common.exception.CustomException;
import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.UpdatePasswordDTO;
import com.lxxgd.bysj02.dto.UpdateUserInfoDTO;
import com.lxxgd.bysj02.dto.UserDTO;
import com.lxxgd.bysj02.service.IUserService;
import com.lxxgd.bysj02.vo.UserPasswordVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@RestController
@RequestMapping("/user")
@Tag(name = "用户管理", description = "用户相关接口")
@Slf4j
public class UserController {

    @Resource
    private IUserService userService;

    @GetMapping("/get/{userId}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户信息")
    public Result<UserDTO> getUserById(@PathVariable Long userId) {
        UserDTO userDTO = userService.getUserById(userId);

        if (userDTO != null) {
            return Result.success(userDTO, "获取用户信息成功");
        } else {
            return Result.fail("获取用户信息失败");
        }
    }

    /**
     * 更新用户信息
     * @param userId 用户ID
     * @param updateUserInfoDTO 更新用户信息DTO
     * @return 更新后的用户信息
     */
    @PutMapping("/upload/{userId}")
    @Operation(summary = "更新用户信息", description = "根据用户ID更新用户信息")
    public Result<UserDTO> updateUserInfo(@PathVariable Long userId, @RequestBody @Valid UpdateUserInfoDTO updateUserInfoDTO) {
        try {
            // 确保路径参数和请求体中的ID一致
            if (!userId.equals(updateUserInfoDTO.getUserId())) {
                return Result.fail("路径参数与请求体中的用户ID不一致");
            }
            
            UserDTO updatedUser = userService.updateUserInfo(updateUserInfoDTO);
            return Result.success(updatedUser, "更新用户信息成功");
        } catch (CustomException e) {
            log.error("更新用户信息失败: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("更新用户信息异常", e);
            return Result.fail("更新用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户密码
     * @param userId 用户ID
     * @return 用户密码信息
     */
    @GetMapping("/password/{userId}")
    @Operation(summary = "获取用户密码", description = "根据用户ID获取用户密码")
    public Result<UserPasswordVO> getUserPassword(@PathVariable Long userId) {
        try {
            UserPasswordVO userPasswordVO = userService.getUserPassword(userId);
            return Result.success(userPasswordVO, "获取用户密码成功");
        } catch (CustomException e) {
            log.error("获取用户密码失败: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取用户密码异常", e);
            return Result.fail("获取用户密码失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户密码
     * @param userId 用户ID
     * @param updatePasswordDTO 更新密码DTO
     * @return 更新后的用户密码信息
     */
    @PutMapping("/password/{userId}")
    @Operation(summary = "更新用户密码", description = "根据用户ID更新用户密码")
    public Result<UserPasswordVO> updateUserPassword(@PathVariable Long userId, @RequestBody @Valid UpdatePasswordDTO updatePasswordDTO) {
        try {
            UserPasswordVO updatedPassword = userService.updateUserPassword(userId, updatePasswordDTO);
            return Result.success(updatedPassword, "更新用户密码成功");
        } catch (CustomException e) {
            log.error("更新用户密码失败: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("更新用户密码异常", e);
            return Result.fail("更新用户密码失败: " + e.getMessage());
        }
    }
}
