package com.lxxgd.bysj02.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lxxgd.bysj02.common.result.PageResult;
import com.lxxgd.bysj02.common.result.Result;
import com.lxxgd.bysj02.dto.TeacherCourseInfoDTO;
import com.lxxgd.bysj02.dto.TeachingCourseInfoDTO;
import com.lxxgd.bysj02.entity.TeachingCourse;
import com.lxxgd.bysj02.service.ITeachingCourseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 教师任课表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@RestController
@RequestMapping("/teachers")
@RequiredArgsConstructor
@Tag(name = "教师授课管理", description = "教师授课相关接口")
@Slf4j
public class TeachingCourseController {

    @Resource
    private final ITeachingCourseService teachingCourseService;

    /**
     * 根据教师ID获取当前学期（状态为2-进行中）的教师授课信息
     * @param teacherId 教师ID
     * @return 教师授课信息列表
     */
    @GetMapping("/{teacherId}/courses")
    @Operation(summary = "获取教师当前学期授课信息", description = "根据教师ID获取当前学期（状态为2-进行中）的教师授课信息")
    public Result<List<TeachingCourseInfoDTO>> getTeachingCourses(@PathVariable Long teacherId) {
        try {
            List<TeachingCourseInfoDTO> teachingCourseInfoList = teachingCourseService.getCurrentSemesterTeachingCoursesByTeacherId(teacherId);
            return Result.success(teachingCourseInfoList, "获取教师当前学期授课信息成功");
        } catch (Exception e) {
            log.error("获取教师当前学期授课信息失败", e);
            return Result.fail("获取教师当前学期授课信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据教师ID和学期名称获取教师授课信息（旧接口，保留向后兼容）
     * @param teacherId 教师ID
     * @param semester 学期名称
     * @return 教师授课信息列表
     */
    @GetMapping("/{teacherId}/courses/by-semester")
    @Operation(summary = "根据学期名称获取教师授课信息", description = "根据教师ID和学期名称获取教师授课信息")
    public Result<List<TeacherCourseInfoDTO>> getTeachingCoursesBySemester(
            @PathVariable Long teacherId,
            @RequestParam(defaultValue = "第一学期") String semester) {
        try {
            List<TeacherCourseInfoDTO> teacherCourseInfoDTO = teachingCourseService.selectTeacherCourseInfoByTeacherIdSemester(teacherId, semester);
            return Result.success(teacherCourseInfoDTO, "获取教师任课信息成功");
        } catch (Exception e) {
            log.error("获取教师任课信息失败", e);
            return Result.fail("获取教师任课信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据教师ID获取该教师的所有学期列表
     * @param teacherId 教师ID
     * @return 学期列表
     */
    @GetMapping("/{teacherId}/semesters")
    @Operation(summary = "获取教师学期列表", description = "根据教师ID获取该教师的所有学期列表")
    public Result<List<String>> getTeacherSemesters(@PathVariable Long teacherId) {
        try {
            List<String> semesters = teachingCourseService.getSemestersByTeacherId(teacherId);
            return Result.success(semesters, "获取教师学期列表成功");
        } catch (Exception e) {
            log.error("获取教师学期列表失败", e);
            return Result.fail("获取教师学期列表失败: " + e.getMessage());
        }
    }
}