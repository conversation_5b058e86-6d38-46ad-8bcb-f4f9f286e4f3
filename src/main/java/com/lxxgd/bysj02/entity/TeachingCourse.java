package com.lxxgd.bysj02.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 教师任课表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@Setter
@TableName("teaching_course")
@Schema(name = "TeachingCourse", description = "教师任课表")
public class TeachingCourse implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "教师ID")
    private Long teacherId;

    @Schema(description = "课程ID")
    private Long courseId;

    @Schema(description = "学期ID")
    private Long semesterId;

    @Schema(description = "上课时间")
    private String classTime;

    @Schema(description = "上课地点")
    private String classLocation;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
