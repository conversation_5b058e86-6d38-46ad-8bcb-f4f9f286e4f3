package com.lxxgd.bysj02.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 课程表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@Setter
@Schema(name = "Course", description = "课程表")
public class Course implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "课程ID")
    @TableId(value = "course_id", type = IdType.AUTO)
    private Long courseId;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课程编码")
    private String courseCode;

    @Schema(description = "课程类型(1-通识课 2-专业课 3-选修课)")
    private Byte courseType;

    @Schema(description = "所属院系ID（通识课可为空）")
    private Long departmentId;

    @Schema(description = "课程描述")
    private String description;

    @Schema(description = "状态(0-禁用 1-启用)")
    private Byte status;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
