package com.lxxgd.bysj02.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 学生信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@Setter
@Schema(name = "Student", description = "学生信息表")
public class Student implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学生ID(关联user表)")
    @TableId("student_id")
    private Long studentId;

    @Schema(description = "学号")
    private String studentCode;

    @Schema(description = "所属院系ID")
    private Long departmentId;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "年级")
    private String grade;

    @Schema(description = "状态(0-毕业 1-在读)")
    private Byte status;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
