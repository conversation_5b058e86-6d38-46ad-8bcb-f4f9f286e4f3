package com.lxxgd.bysj02.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 教师信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@Setter
@Schema(name = "Teacher", description = "教师信息表")
public class Teacher implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "教师ID(关联user表)")
    @TableId("teacher_id")
    private Long teacherId;

    @Schema(description = "教师工号")
    private String teacherCode;

    @Schema(description = "所属院系ID")
    private Long departmentId;

    @Schema(description = "职称")
    private String title;

    @Schema(description = "教龄")
    private Integer teachingYears;

    @Schema(description = "状态(0-离职 1-在职)")
    private Byte status;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
