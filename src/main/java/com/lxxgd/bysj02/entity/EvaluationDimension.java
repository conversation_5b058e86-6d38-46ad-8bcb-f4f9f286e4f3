package com.lxxgd.bysj02.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 评价维度表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@Setter
@TableName("evaluation_dimension")
@Schema(name = "EvaluationDimension", description = "评价维度表")
public class EvaluationDimension implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "维度ID")
    @TableId(value = "dimension_id", type = IdType.AUTO)
    private Long dimensionId;

    @Schema(description = "维度名称")
    private String dimensionName;

    @Schema(description = "权重")
    private BigDecimal weight;

    @Schema(description = "最高分")
    private BigDecimal maxScore;

    @Schema(description = "维度描述")
    private String description;

    @Schema(description = "排序号")
    private Integer sort;

    @Schema(description = "状态(0-禁用 1-启用)")
    private Byte status;

    @Schema(description = "创建人ID")
    private Long createBy;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
