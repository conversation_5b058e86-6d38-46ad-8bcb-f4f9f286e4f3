package com.lxxgd.bysj02.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 院系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Getter
@Setter
@Schema(name = "Department", description = "院系表")
public class Department implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "院系ID")
    @TableId(value = "department_id", type = IdType.AUTO)
    private Long departmentId;

    @Schema(description = "院系名称")
    private String departmentName;

    @Schema(description = "院系编码")
    private String departmentCode;

    @Schema(description = "负责人")
    private String leader;

    @Schema(description = "联系电话")
    private String contactPhone;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
