package com.lxxgd.bysj02;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.lxxgd.bysj02.config.DataSourceProperties;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Types;
import java.util.Collections;

public class CodeGenerator {

    public static void main(String[] args) {
        FastAutoGenerator.create("*******************************************************************************************************************************", "root", "123456")
                .globalConfig(builder -> {
                    builder.author("KiritanTakechi") // 设置作者
                            .enableSpringdoc()
                            .disableOpenDir() // 是否打开输出目录
                            .outputDir(System.getProperty("user.dir")+"/src/main/java"); // 指定输出目录
                })
                .dataSourceConfig(builder ->
                        builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                            if (typeCode == Types.SMALLINT) {
                                // 自定义类型转换
                                return DbColumnType.INTEGER;
                            }
                            return typeRegistry.getColumnType(metaInfo);
                        })
                )
                .packageConfig(builder ->
                        builder.parent("com.lxxgd") // 设置父包名
                                .moduleName("bysj02") // 设置父包模块名
                                .pathInfo(Collections.singletonMap(OutputFile.xml, System.getProperty("user.dir")+"/src/main/resources/mapper")) // 设置mapperXml生成路径

                )
                .strategyConfig(builder ->
                        builder.addInclude() // 设置需要生成的表名
                                .entityBuilder() // 实体类配置
                                .enableLombok() // 是否使用lombok
                                .enableFileOverride() // 开启覆盖模式
                                .build()
                )
                .templateConfig(builder -> {
                    // 只生成实体类，禁用其他文件的生成
                    builder.disable(
                            // 禁用controller生成
                            com.baomidou.mybatisplus.generator.config.TemplateType.CONTROLLER,
                            // 禁用service生成
                            com.baomidou.mybatisplus.generator.config.TemplateType.SERVICE,
                            // 禁用serviceImpl生成
                            com.baomidou.mybatisplus.generator.config.TemplateType.SERVICE_IMPL,
                            // 禁用mapper生成
                            com.baomidou.mybatisplus.generator.config.TemplateType.MAPPER,
                            // 禁用mapperXml生成
                            com.baomidou.mybatisplus.generator.config.TemplateType.XML
                    );
                })
                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}