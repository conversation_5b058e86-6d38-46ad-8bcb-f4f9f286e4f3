package com.lxxgd.bysj02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lxxgd.bysj02.dto.CourseInfoDTO;
import com.lxxgd.bysj02.dto.EvaluationDimensionDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationIndicatorDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationScoreOptionDetailDTO;
import com.lxxgd.bysj02.dto.TeacherInfoDTO;
import com.lxxgd.bysj02.entity.Course;
import com.lxxgd.bysj02.entity.EvaluationDimension;
import com.lxxgd.bysj02.entity.EvaluationIndicator;
import com.lxxgd.bysj02.entity.EvaluationPeriod;
import com.lxxgd.bysj02.entity.EvaluationScoreOption;
import com.lxxgd.bysj02.entity.EvaluationTask;
import com.lxxgd.bysj02.entity.EvaluationTemplate;
import com.lxxgd.bysj02.entity.Teacher;
import com.lxxgd.bysj02.entity.TemplateDimension;
import com.lxxgd.bysj02.entity.User;
import com.lxxgd.bysj02.mapper.EvaluationTaskMapper;
import com.lxxgd.bysj02.service.ICourseService;
import com.lxxgd.bysj02.service.IEvaluationDimensionService;
import com.lxxgd.bysj02.service.IEvaluationIndicatorService;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import com.lxxgd.bysj02.service.IEvaluationScoreOptionService;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import com.lxxgd.bysj02.service.IEvaluationTemplateService;
import com.lxxgd.bysj02.service.ITeacherService;
import com.lxxgd.bysj02.service.ITemplateDimensionService;
import com.lxxgd.bysj02.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 评教任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Service
public class EvaluationTaskServiceImpl extends ServiceImpl<EvaluationTaskMapper, EvaluationTask> implements IEvaluationTaskService {

    @Resource
    private IEvaluationPeriodService evaluationPeriodService;

    @Resource
    private ITeacherService teacherService;

    @Resource
    private IUserService userService;

    @Resource
    private ICourseService courseService;

    @Resource
    private IEvaluationTemplateService evaluationTemplateService;

    @Resource
    private ITemplateDimensionService templateDimensionService;

    @Resource
    private IEvaluationDimensionService evaluationDimensionService;

    @Resource
    private IEvaluationIndicatorService evaluationIndicatorService;

    @Resource
    private IEvaluationScoreOptionService evaluationScoreOptionService;

    @Override
    public boolean hasCurrentEvaluationTask(Long studentId) {
        // 1. 查询该学生是否有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return false;
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 检查任务对应的评教周期是否在有效时间范围内
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodService.getById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public List<TeacherInfoDTO> getEvaluationTeachersByStudentId(Long studentId) {
        // 1. 查询该学生的所有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        // 只查询未评教的任务（status=0）
        queryWrapper.eq(EvaluationTask::getStatus, (byte) 0);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 过滤出在有效评教周期内的任务
        List<EvaluationTask> validTasks = new ArrayList<>();
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodService.getById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                validTasks.add(task);
            }
        }

        if (validTasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 4. 提取教师ID列表（去重）
        Set<Long> teacherIds = validTasks.stream()
                .map(EvaluationTask::getTeacherId)
                .collect(Collectors.toSet());

        // 5. 查询教师信息
        List<Teacher> teachers = teacherService.listByIds(teacherIds);

        // 6. 查询用户信息（获取真实姓名）
        List<User> users = userService.listByIds(teacherIds);
        Map<Long, String> userRealNameMap = users.stream()
                .collect(Collectors.toMap(User::getUserId, User::getRealName));

        // 7. 构建返回结果
        List<TeacherInfoDTO> result = new ArrayList<>();
        for (Teacher teacher : teachers) {
            TeacherInfoDTO teacherInfo = new TeacherInfoDTO();
            teacherInfo.setTeacherId(String.valueOf(teacher.getTeacherId()));
            teacherInfo.setTitle(teacher.getTitle());
            // 从用户表获取真实姓名
            teacherInfo.setRealName(userRealNameMap.get(teacher.getTeacherId()));
            result.add(teacherInfo);
        }

        return result;
    }

    @Override
    public List<CourseInfoDTO> getEvaluationCoursesByStudentAndTeacher(Long studentId, Long teacherId) {
        // 1. 查询该学生和教师的所有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        queryWrapper.eq(EvaluationTask::getTeacherId, teacherId);
        // 只查询未评教的任务（status=0）
        queryWrapper.eq(EvaluationTask::getStatus, (byte) 0);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 过滤出在有效评教周期内的任务
        List<EvaluationTask> validTasks = new ArrayList<>();
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodService.getById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                validTasks.add(task);
            }
        }

        if (validTasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 4. 提取课程ID列表（去重）
        Set<Long> courseIds = validTasks.stream()
                .map(EvaluationTask::getCourseId)
                .collect(Collectors.toSet());

        // 5. 查询课程信息
        List<Course> courses = courseService.listByIds(courseIds);

        // 6. 构建返回结果
        List<CourseInfoDTO> result = new ArrayList<>();
        for (Course course : courses) {
            CourseInfoDTO courseInfo = new CourseInfoDTO();
            courseInfo.setCourseId(String.valueOf(course.getCourseId()));
            courseInfo.setCourseName(course.getCourseName());

            // 处理课程类型
            Byte courseType = course.getCourseType();
            if (courseType != null) {
                switch (courseType) {
                    case 1:
                        courseInfo.setCourseType("通识课");
                        break;
                    case 2:
                        courseInfo.setCourseType("专业课");
                        break;
                    case 3:
                        courseInfo.setCourseType("选修课");
                        break;
                    default:
                        courseInfo.setCourseType("未知");
                }
            } else {
                courseInfo.setCourseType("未知");
            }

            result.add(courseInfo);
        }

        return result;
    }

    @Override
    public List<EvaluationDimensionDetailDTO> getCurrentEvaluationDimensions() {
        // 1. 查询当前生效的评价模板（status=1且is_default=1）
        LambdaQueryWrapper<EvaluationTemplate> templateQueryWrapper = new LambdaQueryWrapper<>();
        templateQueryWrapper.eq(EvaluationTemplate::getStatus, (byte) 1);
        templateQueryWrapper.eq(EvaluationTemplate::getIsDefault, (byte) 1);
        EvaluationTemplate currentTemplate = evaluationTemplateService.getOne(templateQueryWrapper);

        if (currentTemplate == null) {
            return new ArrayList<>();
        }

        // 2. 查询模板维度关联表中对应的维度ID
        LambdaQueryWrapper<TemplateDimension> templateDimensionQueryWrapper = new LambdaQueryWrapper<>();
        templateDimensionQueryWrapper.eq(TemplateDimension::getTemplateId, currentTemplate.getTemplateId());
        templateDimensionQueryWrapper.orderByAsc(TemplateDimension::getSort);
        List<TemplateDimension> templateDimensions = templateDimensionService.list(templateDimensionQueryWrapper);

        if (templateDimensions.isEmpty()) {
            return new ArrayList<>();
        }

        // 3. 提取维度ID列表
        List<Long> dimensionIds = templateDimensions.stream()
                .map(TemplateDimension::getDimensionId)
                .collect(Collectors.toList());

        // 4. 查询评价维度表中的相关数据（只查询启用状态的维度）
        LambdaQueryWrapper<EvaluationDimension> dimensionQueryWrapper = new LambdaQueryWrapper<>();
        dimensionQueryWrapper.in(EvaluationDimension::getDimensionId, dimensionIds);
        dimensionQueryWrapper.eq(EvaluationDimension::getStatus, (byte) 1);
        dimensionQueryWrapper.orderByAsc(EvaluationDimension::getSort);
        List<EvaluationDimension> dimensions = evaluationDimensionService.list(dimensionQueryWrapper);

        if (dimensions.isEmpty()) {
            return new ArrayList<>();
        }

        // 5. 创建维度ID到权重的映射
        Map<Long, TemplateDimension> templateDimensionMap = templateDimensions.stream()
                .collect(Collectors.toMap(TemplateDimension::getDimensionId, td -> td));

        // 6. 查询这些维度下的所有指标（只查询启用状态的指标）
        List<Long> finalDimensionIds = dimensions.stream()
                .map(EvaluationDimension::getDimensionId)
                .collect(Collectors.toList());

        LambdaQueryWrapper<EvaluationIndicator> indicatorQueryWrapper = new LambdaQueryWrapper<>();
        indicatorQueryWrapper.in(EvaluationIndicator::getDimensionId, finalDimensionIds);
        indicatorQueryWrapper.eq(EvaluationIndicator::getStatus, (byte) 1);
        indicatorQueryWrapper.orderByAsc(EvaluationIndicator::getSort);
        List<EvaluationIndicator> indicators = evaluationIndicatorService.list(indicatorQueryWrapper);

        // 7. 查询这些指标下的所有评分选项
        List<Long> indicatorIds = indicators.stream()
                .map(EvaluationIndicator::getIndicatorId)
                .collect(Collectors.toList());

        List<EvaluationScoreOption> scoreOptions = new ArrayList<>();
        if (!indicatorIds.isEmpty()) {
            LambdaQueryWrapper<EvaluationScoreOption> optionQueryWrapper = new LambdaQueryWrapper<>();
            optionQueryWrapper.in(EvaluationScoreOption::getIndicatorId, indicatorIds);
            optionQueryWrapper.orderByAsc(EvaluationScoreOption::getSort);
            scoreOptions = evaluationScoreOptionService.list(optionQueryWrapper);
        }

        // 8. 按维度ID分组指标
        Map<Long, List<EvaluationIndicator>> indicatorMap = indicators.stream()
                .collect(Collectors.groupingBy(EvaluationIndicator::getDimensionId));

        // 9. 按指标ID分组评分选项
        Map<Long, List<EvaluationScoreOption>> optionMap = scoreOptions.stream()
                .collect(Collectors.groupingBy(EvaluationScoreOption::getIndicatorId));

        // 10. 构建返回结果
        List<EvaluationDimensionDetailDTO> result = new ArrayList<>();
        for (EvaluationDimension dimension : dimensions) {
            EvaluationDimensionDetailDTO dimensionDetail = new EvaluationDimensionDetailDTO();
            dimensionDetail.setDimension_id(String.valueOf(dimension.getDimensionId()));
            dimensionDetail.setDimension_name(dimension.getDimensionName());

            // 从模板维度关联表获取权重
            TemplateDimension templateDimension = templateDimensionMap.get(dimension.getDimensionId());
            if (templateDimension != null) {
                dimensionDetail.setWeight(templateDimension.getWeight());
            }

            // 构建指标列表
            List<EvaluationIndicatorDetailDTO> indicatorDetails = new ArrayList<>();
            List<EvaluationIndicator> dimensionIndicators = indicatorMap.get(dimension.getDimensionId());
            if (dimensionIndicators != null) {
                for (EvaluationIndicator indicator : dimensionIndicators) {
                    EvaluationIndicatorDetailDTO indicatorDetail = new EvaluationIndicatorDetailDTO();
                    indicatorDetail.setIndicator_id(String.valueOf(indicator.getIndicatorId()));
                    indicatorDetail.setIndicator_name(indicator.getIndicatorName());
                    indicatorDetail.setWeight(indicator.getWeight());
                    indicatorDetail.setMax_score(indicator.getMaxScore());
                    indicatorDetail.setMin_score(indicator.getMinScore());

                    // 处理评分类型
                    Byte scoringType = indicator.getScoringType();
                    if (scoringType != null) {
                        switch (scoringType) {
                            case 1:
                                indicatorDetail.setScoring_type("score");
                                break;
                            case 2:
                                indicatorDetail.setScoring_type("range");
                                break;
                            case 3:
                                indicatorDetail.setScoring_type("option");
                                break;
                            default:
                                indicatorDetail.setScoring_type("score");
                        }
                    } else {
                        indicatorDetail.setScoring_type("score");
                    }

                    // 构建评分选项列表（仅当scoring_type为'option'时）
                    if ("option".equals(indicatorDetail.getScoring_type())) {
                        List<EvaluationScoreOptionDetailDTO> optionDetails = new ArrayList<>();
                        List<EvaluationScoreOption> indicatorOptions = optionMap.get(indicator.getIndicatorId());
                        if (indicatorOptions != null) {
                            for (EvaluationScoreOption option : indicatorOptions) {
                                EvaluationScoreOptionDetailDTO optionDetail = new EvaluationScoreOptionDetailDTO();
                                optionDetail.setOption_id(String.valueOf(option.getOptionId()));
                                optionDetail.setScore(option.getScore());
                                optionDetail.setScore_description(option.getScoreDescription());
                                optionDetail.setSort(option.getSort());
                                optionDetails.add(optionDetail);
                            }
                        }
                        indicatorDetail.setOptions(optionDetails);
                    }

                    indicatorDetails.add(indicatorDetail);
                }
            }

            dimensionDetail.setIndicators(indicatorDetails);
            result.add(dimensionDetail);
        }

        return result;
    }
}
