package com.lxxgd.bysj02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lxxgd.bysj02.dto.CourseInfoDTO;
import com.lxxgd.bysj02.dto.DimensionScoreDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationDimensionDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationIndicatorDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationRecordDetailDTO;
import com.lxxgd.bysj02.dto.EvaluationScoreOptionDetailDTO;
import com.lxxgd.bysj02.dto.StudentEvaluationSubmissionDTO;
import com.lxxgd.bysj02.dto.TeacherCourseDTO;
import com.lxxgd.bysj02.dto.TeacherInfoDTO;
import com.lxxgd.bysj02.entity.Course;
import com.lxxgd.bysj02.entity.CourseEvaluationSummary;
import com.lxxgd.bysj02.entity.EvaluationDimension;
import com.lxxgd.bysj02.entity.EvaluationIndicator;
import com.lxxgd.bysj02.entity.EvaluationPeriod;
import com.lxxgd.bysj02.entity.EvaluationRecord;
import com.lxxgd.bysj02.entity.EvaluationRecordDetail;
import com.lxxgd.bysj02.entity.EvaluationScoreOption;
import com.lxxgd.bysj02.entity.EvaluationTask;
import com.lxxgd.bysj02.entity.EvaluationTemplate;
import com.lxxgd.bysj02.entity.Teacher;
import com.lxxgd.bysj02.entity.TemplateDimension;
import com.lxxgd.bysj02.entity.User;
import com.lxxgd.bysj02.mapper.EvaluationPeriodMapper;
import com.lxxgd.bysj02.mapper.EvaluationTaskMapper;
import com.lxxgd.bysj02.service.ICourseEvaluationSummaryService;
import com.lxxgd.bysj02.service.ICourseService;
import com.lxxgd.bysj02.service.IEvaluationDimensionService;
import com.lxxgd.bysj02.service.IEvaluationIndicatorService;
import com.lxxgd.bysj02.service.IEvaluationRecordDetailService;
import com.lxxgd.bysj02.service.IEvaluationRecordService;
import com.lxxgd.bysj02.service.IEvaluationScoreOptionService;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import com.lxxgd.bysj02.service.IEvaluationTemplateService;
import com.lxxgd.bysj02.service.ITeacherService;
import com.lxxgd.bysj02.service.ITemplateDimensionService;
import com.lxxgd.bysj02.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 评教任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Service
@Slf4j
public class EvaluationTaskServiceImpl extends ServiceImpl<EvaluationTaskMapper, EvaluationTask> implements IEvaluationTaskService {

    @Resource
    private EvaluationPeriodMapper evaluationPeriodMapper;

    @Resource
    private ITeacherService teacherService;

    @Resource
    private IUserService userService;

    @Resource
    private ICourseService courseService;

    @Resource
    private IEvaluationTemplateService evaluationTemplateService;

    @Resource
    private ITemplateDimensionService templateDimensionService;

    @Resource
    private IEvaluationDimensionService evaluationDimensionService;

    @Resource
    private IEvaluationIndicatorService evaluationIndicatorService;

    @Resource
    private IEvaluationScoreOptionService evaluationScoreOptionService;

    @Resource
    private IEvaluationRecordService evaluationRecordService;

    @Resource
    private IEvaluationRecordDetailService evaluationRecordDetailService;

    @Resource
    private ICourseEvaluationSummaryService courseEvaluationSummaryService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean hasCurrentEvaluationTask(Long studentId) {
        // 1. 查询该学生是否有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return false;
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 检查任务对应的评教周期是否在有效时间范围内
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodMapper.selectById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public List<TeacherInfoDTO> getEvaluationTeachersByStudentId(Long studentId) {
        // 1. 查询该学生的所有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        // 只查询未评教的任务（status=0）
        queryWrapper.eq(EvaluationTask::getStatus, (byte) 0);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 过滤出在有效评教周期内的任务
        List<EvaluationTask> validTasks = new ArrayList<>();
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodMapper.selectById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                validTasks.add(task);
            }
        }

        if (validTasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 4. 提取教师ID列表（去重）
        Set<Long> teacherIds = validTasks.stream()
                .map(EvaluationTask::getTeacherId)
                .collect(Collectors.toSet());

        // 5. 查询教师信息
        List<Teacher> teachers = teacherService.listByIds(teacherIds);

        // 6. 查询用户信息（获取真实姓名）
        List<User> users = userService.listByIds(teacherIds);
        Map<Long, String> userRealNameMap = users.stream()
                .collect(Collectors.toMap(User::getUserId, User::getRealName));

        // 7. 构建返回结果
        List<TeacherInfoDTO> result = new ArrayList<>();
        for (Teacher teacher : teachers) {
            TeacherInfoDTO teacherInfo = new TeacherInfoDTO();
            teacherInfo.setTeacherId(String.valueOf(teacher.getTeacherId()));
            teacherInfo.setTitle(teacher.getTitle());
            // 从用户表获取真实姓名
            teacherInfo.setRealName(userRealNameMap.get(teacher.getTeacherId()));
            result.add(teacherInfo);
        }

        return result;
    }

    @Override
    public List<CourseInfoDTO> getEvaluationCoursesByStudentAndTeacher(Long studentId, Long teacherId) {
        // 1. 查询该学生和教师的所有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        queryWrapper.eq(EvaluationTask::getTeacherId, teacherId);
        // 只查询未评教的任务（status=0）
        queryWrapper.eq(EvaluationTask::getStatus, (byte) 0);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 过滤出在有效评教周期内的任务
        List<EvaluationTask> validTasks = new ArrayList<>();
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodMapper.selectById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                validTasks.add(task);
            }
        }

        if (validTasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 4. 提取课程ID列表（去重）
        Set<Long> courseIds = validTasks.stream()
                .map(EvaluationTask::getCourseId)
                .collect(Collectors.toSet());

        // 5. 查询课程信息
        List<Course> courses = courseService.listByIds(courseIds);

        // 6. 构建返回结果
        List<CourseInfoDTO> result = new ArrayList<>();
        for (Course course : courses) {
            CourseInfoDTO courseInfo = new CourseInfoDTO();
            courseInfo.setCourseId(String.valueOf(course.getCourseId()));
            courseInfo.setCourseName(course.getCourseName());

            // 处理课程类型
            Byte courseType = course.getCourseType();
            if (courseType != null) {
                switch (courseType) {
                    case 1:
                        courseInfo.setCourseType("通识课");
                        break;
                    case 2:
                        courseInfo.setCourseType("专业课");
                        break;
                    case 3:
                        courseInfo.setCourseType("选修课");
                        break;
                    default:
                        courseInfo.setCourseType("未知");
                }
            } else {
                courseInfo.setCourseType("未知");
            }

            result.add(courseInfo);
        }

        return result;
    }

    @Override
    public List<EvaluationDimensionDetailDTO> getCurrentEvaluationDimensions() {
        // 1. 查询当前生效的评价模板（status=1且is_default=1）
        LambdaQueryWrapper<EvaluationTemplate> templateQueryWrapper = new LambdaQueryWrapper<>();
        templateQueryWrapper.eq(EvaluationTemplate::getStatus, (byte) 1);
        templateQueryWrapper.eq(EvaluationTemplate::getIsDefault, (byte) 1);
        EvaluationTemplate currentTemplate = evaluationTemplateService.getOne(templateQueryWrapper);

        if (currentTemplate == null) {
            return new ArrayList<>();
        }

        // 2. 查询模板维度关联表中对应的维度ID
        LambdaQueryWrapper<TemplateDimension> templateDimensionQueryWrapper = new LambdaQueryWrapper<>();
        templateDimensionQueryWrapper.eq(TemplateDimension::getTemplateId, currentTemplate.getTemplateId());
        templateDimensionQueryWrapper.orderByAsc(TemplateDimension::getSort);
        List<TemplateDimension> templateDimensions = templateDimensionService.list(templateDimensionQueryWrapper);

        if (templateDimensions.isEmpty()) {
            return new ArrayList<>();
        }

        // 3. 提取维度ID列表
        List<Long> dimensionIds = templateDimensions.stream()
                .map(TemplateDimension::getDimensionId)
                .collect(Collectors.toList());

        // 4. 查询评价维度表中的相关数据（只查询启用状态的维度）
        LambdaQueryWrapper<EvaluationDimension> dimensionQueryWrapper = new LambdaQueryWrapper<>();
        dimensionQueryWrapper.in(EvaluationDimension::getDimensionId, dimensionIds);
        dimensionQueryWrapper.eq(EvaluationDimension::getStatus, (byte) 1);
        dimensionQueryWrapper.orderByAsc(EvaluationDimension::getSort);
        List<EvaluationDimension> dimensions = evaluationDimensionService.list(dimensionQueryWrapper);

        if (dimensions.isEmpty()) {
            return new ArrayList<>();
        }

        // 5. 创建维度ID到权重的映射
        Map<Long, TemplateDimension> templateDimensionMap = templateDimensions.stream()
                .collect(Collectors.toMap(TemplateDimension::getDimensionId, td -> td));

        // 6. 查询这些维度下的所有指标（只查询启用状态的指标）
        List<Long> finalDimensionIds = dimensions.stream()
                .map(EvaluationDimension::getDimensionId)
                .collect(Collectors.toList());

        LambdaQueryWrapper<EvaluationIndicator> indicatorQueryWrapper = new LambdaQueryWrapper<>();
        indicatorQueryWrapper.in(EvaluationIndicator::getDimensionId, finalDimensionIds);
        indicatorQueryWrapper.eq(EvaluationIndicator::getStatus, (byte) 1);
        indicatorQueryWrapper.orderByAsc(EvaluationIndicator::getSort);
        List<EvaluationIndicator> indicators = evaluationIndicatorService.list(indicatorQueryWrapper);

        // 7. 查询这些指标下的所有评分选项
        List<Long> indicatorIds = indicators.stream()
                .map(EvaluationIndicator::getIndicatorId)
                .collect(Collectors.toList());

        List<EvaluationScoreOption> scoreOptions = new ArrayList<>();
        if (!indicatorIds.isEmpty()) {
            LambdaQueryWrapper<EvaluationScoreOption> optionQueryWrapper = new LambdaQueryWrapper<>();
            optionQueryWrapper.in(EvaluationScoreOption::getIndicatorId, indicatorIds);
            optionQueryWrapper.orderByAsc(EvaluationScoreOption::getSort);
            scoreOptions = evaluationScoreOptionService.list(optionQueryWrapper);
        }

        // 8. 按维度ID分组指标
        Map<Long, List<EvaluationIndicator>> indicatorMap = indicators.stream()
                .collect(Collectors.groupingBy(EvaluationIndicator::getDimensionId));

        // 9. 按指标ID分组评分选项
        Map<Long, List<EvaluationScoreOption>> optionMap = scoreOptions.stream()
                .collect(Collectors.groupingBy(EvaluationScoreOption::getIndicatorId));

        // 10. 构建返回结果
        List<EvaluationDimensionDetailDTO> result = new ArrayList<>();
        for (EvaluationDimension dimension : dimensions) {
            EvaluationDimensionDetailDTO dimensionDetail = new EvaluationDimensionDetailDTO();
            dimensionDetail.setDimension_id(String.valueOf(dimension.getDimensionId()));
            dimensionDetail.setDimension_name(dimension.getDimensionName());

            // 从模板维度关联表获取权重
            TemplateDimension templateDimension = templateDimensionMap.get(dimension.getDimensionId());
            if (templateDimension != null) {
                dimensionDetail.setWeight(templateDimension.getWeight());
            }

            // 构建指标列表
            List<EvaluationIndicatorDetailDTO> indicatorDetails = new ArrayList<>();
            List<EvaluationIndicator> dimensionIndicators = indicatorMap.get(dimension.getDimensionId());
            if (dimensionIndicators != null) {
                for (EvaluationIndicator indicator : dimensionIndicators) {
                    EvaluationIndicatorDetailDTO indicatorDetail = new EvaluationIndicatorDetailDTO();
                    indicatorDetail.setIndicator_id(String.valueOf(indicator.getIndicatorId()));
                    indicatorDetail.setIndicator_name(indicator.getIndicatorName());
                    indicatorDetail.setWeight(indicator.getWeight());
                    indicatorDetail.setMax_score(indicator.getMaxScore());
                    indicatorDetail.setMin_score(indicator.getMinScore());

                    // 处理评分类型
                    Byte scoringType = indicator.getScoringType();
                    if (scoringType != null) {
                        switch (scoringType) {
                            case 1:
                                indicatorDetail.setScoring_type("score");
                                break;
                            case 2:
                                indicatorDetail.setScoring_type("range");
                                break;
                            case 3:
                                indicatorDetail.setScoring_type("option");
                                break;
                            default:
                                indicatorDetail.setScoring_type("score");
                        }
                    } else {
                        indicatorDetail.setScoring_type("score");
                    }

                    // 构建评分选项列表（仅当scoring_type为'option'时）
                    if ("option".equals(indicatorDetail.getScoring_type())) {
                        List<EvaluationScoreOptionDetailDTO> optionDetails = new ArrayList<>();
                        List<EvaluationScoreOption> indicatorOptions = optionMap.get(indicator.getIndicatorId());
                        if (indicatorOptions != null) {
                            for (EvaluationScoreOption option : indicatorOptions) {
                                EvaluationScoreOptionDetailDTO optionDetail = new EvaluationScoreOptionDetailDTO();
                                optionDetail.setOption_id(String.valueOf(option.getOptionId()));
                                optionDetail.setScore(option.getScore());
                                optionDetail.setScore_description(option.getScoreDescription());
                                optionDetail.setSort(option.getSort());
                                optionDetails.add(optionDetail);
                            }
                        }
                        indicatorDetail.setOptions(optionDetails);
                    }

                    indicatorDetails.add(indicatorDetail);
                }
            }

            dimensionDetail.setIndicators(indicatorDetails);
            result.add(dimensionDetail);
        }

        return result;
    }

    @Override
    public Long getEvaluationTaskId(Long studentId, Long teacherId, Long courseId) {
        // 1. 查询符合条件的评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        queryWrapper.eq(EvaluationTask::getTeacherId, teacherId);
        queryWrapper.eq(EvaluationTask::getCourseId, courseId);
        // 只查询未评教的任务（status=0）
        queryWrapper.eq(EvaluationTask::getStatus, (byte) 0);

        EvaluationTask task = this.getOne(queryWrapper);

        if (task == null) {
            return null;
        }

        // 2. 检查任务是否在有效的评教周期内
        Long periodId = task.getPeriodId();
        EvaluationPeriod period = evaluationPeriodMapper.selectById(periodId);

        if (period == null) {
            return null;
        }

        // 3. 检查当前时间是否在评教周期范围内
        LocalDateTime now = LocalDateTime.now();
        if (period.getStartTime().isBefore(now) && period.getEndTime().isAfter(now)) {
            return task.getTaskId();
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitStudentEvaluation(StudentEvaluationSubmissionDTO submissionDTO) {
        try {
            Long taskId = Long.valueOf(submissionDTO.getTask_id());

            // 1. 验证评教任务是否存在且未评教
            EvaluationTask task = this.getById(taskId);
            if (task == null) {
                throw new RuntimeException("评教任务不存在");
            }
            if (task.getStatus() != 0) {
                throw new RuntimeException("该评教任务已完成，不能重复提交");
            }

            // 2. 验证评教任务是否在有效期内
            EvaluationPeriod period = evaluationPeriodMapper.selectById(task.getPeriodId());
            if (period == null) {
                throw new RuntimeException("评教周期不存在");
            }
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(period.getStartTime()) || now.isAfter(period.getEndTime())) {
                throw new RuntimeException("当前不在评教周期内");
            }

            // 3. 创建评价总记录
            EvaluationRecord record = new EvaluationRecord();
            record.setTaskId(taskId);
            record.setTotalScore(submissionDTO.getTotal_score());
            record.setComment(submissionDTO.getComment());
            record.setSubmitTime(now);
            record.setCreateTime(now);
            record.setUpdateTime(now);

            boolean recordSaved = evaluationRecordService.save(record);
            if (!recordSaved) {
                throw new RuntimeException("保存评价记录失败");
            }

            // 4. 创建评价详细记录
            List<EvaluationRecordDetail> details = new ArrayList<>();
            for (EvaluationRecordDetailDTO detailDTO : submissionDTO.getDetails()) {
                EvaluationRecordDetail detail = new EvaluationRecordDetail();
                detail.setRecordId(record.getRecordId());
                detail.setIndicatorId(Long.valueOf(detailDTO.getIndicator_id()));
                detail.setScore(detailDTO.getScore());
                detail.setComment(detailDTO.getComment());
                detail.setCreateTime(now);
                details.add(detail);
            }

            boolean detailsSaved = evaluationRecordDetailService.saveBatch(details);
            if (!detailsSaved) {
                throw new RuntimeException("保存评价详细记录失败");
            }

            // 5. 更新课程评价汇总数据中的维度得分
            updateCourseDimensionScores(task, submissionDTO.getDimensionScores());

            // 6. 更新评教任务状态为已评教
            task.setStatus((byte) 1);
            task.setUpdateTime(now);
            boolean taskUpdated = this.updateById(task);
            if (!taskUpdated) {
                throw new RuntimeException("更新评教任务状态失败");
            }

            return true;
        } catch (Exception e) {
            throw new RuntimeException("提交评价失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TeacherCourseDTO> getTeacherCoursesByPeriod(Long periodId, Long teacherId) {
        // 1. 从evaluation_task中查找对应periodId和teacherId的记录中的courseId
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getPeriodId, periodId);
        queryWrapper.eq(EvaluationTask::getTeacherId, teacherId);
        queryWrapper.select(EvaluationTask::getCourseId);

        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 提取课程ID列表（去重）
        Set<Long> courseIds = tasks.stream()
                .map(EvaluationTask::getCourseId)
                .collect(Collectors.toSet());

        // 3. 使用courseID查询课程详细信息
        List<Course> courses = courseService.listByIds(courseIds);

        // 4. 构建返回结果
        List<TeacherCourseDTO> result = new ArrayList<>();
        for (Course course : courses) {
            TeacherCourseDTO teacherCourse = new TeacherCourseDTO();
            teacherCourse.setCourse_id(course.getCourseId().intValue());
            teacherCourse.setCourse_name(course.getCourseName());
            teacherCourse.setCourse_code(course.getCourseCode());
            result.add(teacherCourse);
        }

        return result;
    }

    /**
     * 更新课程评价汇总数据中的维度得分
     * 根据taskId查找到该task对应的periodId、teacherId、courseId后
     * 使用各维度得分详情与旧记录加在一起除以total_count算平均值方式更新dimension_score
     *
     * @param task 评教任务
     * @param dimensionScores 维度得分详情列表
     */
    private void updateCourseDimensionScores(EvaluationTask task, List<DimensionScoreDetailDTO> dimensionScores) {
        try {
            Long periodId = task.getPeriodId();
            Long teacherId = task.getTeacherId();
            Long courseId = task.getCourseId();

            // 1. 查询现有的课程评价汇总记录
            LambdaQueryWrapper<CourseEvaluationSummary> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CourseEvaluationSummary::getPeriodId, periodId)
                       .eq(CourseEvaluationSummary::getTeacherId, teacherId)
                       .eq(CourseEvaluationSummary::getCourseId, courseId);

            CourseEvaluationSummary summary = courseEvaluationSummaryService.getOne(queryWrapper);

            if (summary == null) {
                // 如果不存在汇总记录，创建新的记录
                summary = new CourseEvaluationSummary();
                summary.setPeriodId(periodId);
                summary.setTeacherId(teacherId);
                summary.setCourseId(courseId);
                summary.setTotalScore(BigDecimal.ZERO);
                summary.setTotalCount(0);
                summary.setDimensionScores("{}");
                summary.setCreateTime(LocalDateTime.now());
                summary.setUpdateTime(LocalDateTime.now());
            }

            // 2. 解析现有的维度得分数据
            Map<String, BigDecimal> existingDimensionScores = new HashMap<>();
            if (StringUtils.hasText(summary.getDimensionScores())) {
                try {
                    existingDimensionScores = objectMapper.readValue(
                        summary.getDimensionScores(),
                        new TypeReference<Map<String, BigDecimal>>() {}
                    );
                } catch (Exception e) {
                    log.warn("解析现有维度得分数据失败，将使用空数据: {}", e.getMessage());
                    existingDimensionScores = new HashMap<>();
                }
            }

            // 3. 计算新的维度得分（加权平均）
            int currentCount = summary.getTotalCount();
            int newCount = currentCount + 1;

            Map<String, BigDecimal> updatedDimensionScores = new HashMap<>(existingDimensionScores);

            for (DimensionScoreDetailDTO dimensionScore : dimensionScores) {
                String dimensionId = dimensionScore.getDimension_id();
                BigDecimal newScore = dimensionScore.getScore();

                if (newScore != null) {
                    BigDecimal existingScore = existingDimensionScores.getOrDefault(dimensionId, BigDecimal.ZERO);

                    // 计算加权平均：(旧得分 * 旧数量 + 新得分) / 新数量
                    BigDecimal totalScore = existingScore.multiply(BigDecimal.valueOf(currentCount)).add(newScore);
                    BigDecimal averageScore = totalScore.divide(BigDecimal.valueOf(newCount), 2, BigDecimal.ROUND_HALF_UP);

                    updatedDimensionScores.put(dimensionId, averageScore);
                }
            }

            // 4. 更新汇总记录
            summary.setTotalCount(newCount);
            summary.setUpdateTime(LocalDateTime.now());

            // 将更新后的维度得分转换为JSON字符串
            try {
                String dimensionScoresJson = objectMapper.writeValueAsString(updatedDimensionScores);
                summary.setDimensionScores(dimensionScoresJson);
            } catch (Exception e) {
                log.error("序列化维度得分数据失败", e);
                throw new RuntimeException("更新维度得分数据失败");
            }

            // 5. 保存或更新汇总记录
            boolean success;
            if (summary.getSummaryId() == null) {
                success = courseEvaluationSummaryService.save(summary);
            } else {
                success = courseEvaluationSummaryService.updateById(summary);
            }

            if (!success) {
                throw new RuntimeException("保存课程评价汇总数据失败");
            }

            log.info("成功更新课程评价汇总数据: periodId={}, teacherId={}, courseId={}, newCount={}",
                    periodId, teacherId, courseId, newCount);

        } catch (Exception e) {
            log.error("更新课程评价汇总数据失败", e);
            throw new RuntimeException("更新课程评价汇总数据失败: " + e.getMessage());
        }
    }
}
