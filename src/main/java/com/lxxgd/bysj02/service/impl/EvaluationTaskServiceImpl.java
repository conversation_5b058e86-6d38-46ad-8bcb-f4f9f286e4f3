package com.lxxgd.bysj02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lxxgd.bysj02.dto.CourseInfoDTO;
import com.lxxgd.bysj02.dto.TeacherInfoDTO;
import com.lxxgd.bysj02.entity.Course;
import com.lxxgd.bysj02.entity.EvaluationPeriod;
import com.lxxgd.bysj02.entity.EvaluationTask;
import com.lxxgd.bysj02.entity.Teacher;
import com.lxxgd.bysj02.entity.User;
import com.lxxgd.bysj02.mapper.EvaluationTaskMapper;
import com.lxxgd.bysj02.service.ICourseService;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import com.lxxgd.bysj02.service.ITeacherService;
import com.lxxgd.bysj02.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 评教任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Service
public class EvaluationTaskServiceImpl extends ServiceImpl<EvaluationTaskMapper, EvaluationTask> implements IEvaluationTaskService {

    @Resource
    private IEvaluationPeriodService evaluationPeriodService;

    @Resource
    private ITeacherService teacherService;

    @Resource
    private IUserService userService;

    @Resource
    private ICourseService courseService;

    @Override
    public boolean hasCurrentEvaluationTask(Long studentId) {
        // 1. 查询该学生是否有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return false;
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 检查任务对应的评教周期是否在有效时间范围内
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodService.getById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public List<TeacherInfoDTO> getEvaluationTeachersByStudentId(Long studentId) {
        // 1. 查询该学生的所有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        // 只查询未评教的任务（status=0）
        queryWrapper.eq(EvaluationTask::getStatus, (byte) 0);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 过滤出在有效评教周期内的任务
        List<EvaluationTask> validTasks = new ArrayList<>();
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodService.getById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                validTasks.add(task);
            }
        }

        if (validTasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 4. 提取教师ID列表（去重）
        Set<Long> teacherIds = validTasks.stream()
                .map(EvaluationTask::getTeacherId)
                .collect(Collectors.toSet());

        // 5. 查询教师信息
        List<Teacher> teachers = teacherService.listByIds(teacherIds);

        // 6. 查询用户信息（获取真实姓名）
        List<User> users = userService.listByIds(teacherIds);
        Map<Long, String> userRealNameMap = users.stream()
                .collect(Collectors.toMap(User::getUserId, User::getRealName));

        // 7. 构建返回结果
        List<TeacherInfoDTO> result = new ArrayList<>();
        for (Teacher teacher : teachers) {
            TeacherInfoDTO teacherInfo = new TeacherInfoDTO();
            teacherInfo.setTeacherId(String.valueOf(teacher.getTeacherId()));
            teacherInfo.setTitle(teacher.getTitle());
            // 从用户表获取真实姓名
            teacherInfo.setRealName(userRealNameMap.get(teacher.getTeacherId()));
            result.add(teacherInfo);
        }

        return result;
    }

    @Override
    public List<CourseInfoDTO> getEvaluationCoursesByStudentAndTeacher(Long studentId, Long teacherId) {
        // 1. 查询该学生和教师的所有评教任务
        LambdaQueryWrapper<EvaluationTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EvaluationTask::getStudentId, studentId);
        queryWrapper.eq(EvaluationTask::getTeacherId, teacherId);
        // 只查询未评教的任务（status=0）
        queryWrapper.eq(EvaluationTask::getStatus, (byte) 0);
        List<EvaluationTask> tasks = this.list(queryWrapper);

        if (tasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 3. 过滤出在有效评教周期内的任务
        List<EvaluationTask> validTasks = new ArrayList<>();
        for (EvaluationTask task : tasks) {
            Long periodId = task.getPeriodId();
            EvaluationPeriod period = evaluationPeriodService.getById(periodId);

            if (period != null &&
                period.getStartTime().isBefore(now) &&
                period.getEndTime().isAfter(now)) {
                validTasks.add(task);
            }
        }

        if (validTasks.isEmpty()) {
            return new ArrayList<>();
        }

        // 4. 提取课程ID列表（去重）
        Set<Long> courseIds = validTasks.stream()
                .map(EvaluationTask::getCourseId)
                .collect(Collectors.toSet());

        // 5. 查询课程信息
        List<Course> courses = courseService.listByIds(courseIds);

        // 6. 构建返回结果
        List<CourseInfoDTO> result = new ArrayList<>();
        for (Course course : courses) {
            CourseInfoDTO courseInfo = new CourseInfoDTO();
            courseInfo.setCourseId(String.valueOf(course.getCourseId()));
            courseInfo.setCourseName(course.getCourseName());

            // 处理课程类型
            Byte courseType = course.getCourseType();
            if (courseType != null) {
                switch (courseType) {
                    case 1:
                        courseInfo.setCourseType("通识课");
                        break;
                    case 2:
                        courseInfo.setCourseType("专业课");
                        break;
                    case 3:
                        courseInfo.setCourseType("选修课");
                        break;
                    default:
                        courseInfo.setCourseType("未知");
                }
            } else {
                courseInfo.setCourseType("未知");
            }

            result.add(courseInfo);
        }

        return result;
    }
}
