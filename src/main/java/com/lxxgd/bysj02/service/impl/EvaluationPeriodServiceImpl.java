package com.lxxgd.bysj02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.lxxgd.bysj02.common.exception.CustomException;
import com.lxxgd.bysj02.common.result.PageResult;
import com.lxxgd.bysj02.dto.EvaluationPeriodDTO;
import com.lxxgd.bysj02.dto.EvaluationPeriodListDTO;
import com.lxxgd.bysj02.dto.PeriodFormDataDTO;
import com.lxxgd.bysj02.dto.PeriodListParamsDTO;
import com.lxxgd.bysj02.dto.PublishTaskResultDTO;
import com.lxxgd.bysj02.dto.mapper.EvaluationPeriodDTOMapper;
import com.lxxgd.bysj02.entity.EvaluationPeriod;
import com.lxxgd.bysj02.entity.EvaluationTask;
import com.lxxgd.bysj02.entity.Semester;
import com.lxxgd.bysj02.entity.StudentCourse;
import com.lxxgd.bysj02.mapper.EvaluationPeriodMapper;
import com.lxxgd.bysj02.service.IEvaluationPeriodService;
import com.lxxgd.bysj02.service.IEvaluationTaskService;
import com.lxxgd.bysj02.service.ISemesterService;
import com.lxxgd.bysj02.service.IStudentCourseService;
import com.lxxgd.bysj02.util.PageUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 评教周期表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Service
public class EvaluationPeriodServiceImpl extends ServiceImpl<EvaluationPeriodMapper, EvaluationPeriod> implements IEvaluationPeriodService {

    @Resource
    private ISemesterService semesterService;

    @Resource
    private IEvaluationTaskService evaluationTaskService;

    @Resource
    private IStudentCourseService studentCourseService;

    @Resource
    private EvaluationPeriodDTOMapper evaluationPeriodDTOMapper;

    @Override
    public PageResult<EvaluationPeriodDTO> getPeriodList(PeriodListParamsDTO params) {
        // 1. 构建查询条件
        MPJLambdaWrapper<EvaluationPeriod> wrapper = new MPJLambdaWrapper<EvaluationPeriod>()
                .selectAll(EvaluationPeriod.class)
                .select(Semester::getSemesterName)
                .leftJoin(Semester.class, Semester::getSemesterId, EvaluationPeriod::getSemesterId);

        // 2. 添加学期名称搜索条件
        if (StringUtils.hasText(params.getSemester())) {
            wrapper.like(Semester::getSemesterName, params.getSemester());
        }

        // 3. 按创建时间降序排序
        wrapper.orderByDesc(EvaluationPeriod::getCreateTime);

        // 4. 执行分页查询
        Page<Map<String, Object>> page = new Page<>(params.getPage(), params.getLimit());
        Page<Map<String, Object>> resultPage = baseMapper.selectJoinMapsPage(page, wrapper);

        // 5. 转换查询结果为DTO
        List<EvaluationPeriodDTO> periodDTOList = new ArrayList<>();
        for (Map<String, Object> map : resultPage.getRecords()) {
            EvaluationPeriod period = new EvaluationPeriod();
            period.setPeriodId(Long.valueOf(map.get("period_id").toString()));
            period.setSemesterId(Long.valueOf(map.get("semester_id").toString()));
            period.setStartTime(map.get("start_time") == null ? null : (java.time.LocalDateTime) map.get("start_time"));
            period.setEndTime(map.get("end_time") == null ? null : (java.time.LocalDateTime) map.get("end_time"));
            period.setStatus(map.get("status") == null ? null : Byte.valueOf(map.get("status").toString()));
            period.setCreateBy(map.get("create_by") == null ? null : Long.valueOf(map.get("create_by").toString()));
            period.setCreateTime(map.get("create_time") == null ? null : (java.time.LocalDateTime) map.get("create_time"));
            period.setUpdateTime(map.get("update_time") == null ? null : (java.time.LocalDateTime) map.get("update_time"));

            Semester semester = new Semester();
            semester.setSemesterName((String) map.get("semester_name"));

            EvaluationPeriodDTO periodDTO = evaluationPeriodDTOMapper.toDto(period, semester);
            periodDTOList.add(periodDTO);
        }

        // 6. 构建分页结果
        return PageUtils.toPageResult(periodDTOList, resultPage.getTotal());
    }

    @Override
    public EvaluationPeriodDTO createPeriod(PeriodFormDataDTO periodFormDataDTO) {
        // 1. 根据学期名称查询学期ID
        Semester semester = semesterService.lambdaQuery()
                .eq(Semester::getSemesterName, periodFormDataDTO.getSemester())
                .one();

        if (semester == null) {
            throw new CustomException("学期不存在: " + periodFormDataDTO.getSemester());
        }

        // 2. 检查该学期是否已经存在评教周期
        boolean exists = this.lambdaQuery()
                .eq(EvaluationPeriod::getSemesterId, semester.getSemesterId())
                .exists();

        if (exists) {
            throw new CustomException("该学期已存在评教周期，不能重复创建");
        }

        // 3. 解析开始时间和结束时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime;
        LocalDateTime endTime;

        try {
            startTime = LocalDateTime.parse(periodFormDataDTO.getStart_time(), formatter);
            endTime = LocalDateTime.parse(periodFormDataDTO.getEnd_time(), formatter);
        } catch (Exception e) {
            throw new CustomException("时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        }

        // 4. 检查开始时间是否早于结束时间
        if (startTime.isAfter(endTime)) {
            throw new CustomException("开始时间不能晚于结束时间");
        }

        // 5. 创建评教周期
        EvaluationPeriod period = new EvaluationPeriod();
        period.setSemesterId(semester.getSemesterId());
        period.setStartTime(startTime);
        period.setEndTime(endTime);

        // 6. 设置状态：根据当前时间判断状态
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(startTime)) {
            // 未开始
            period.setStatus((byte) 0);
        } else if (now.isAfter(endTime)) {
            // 已结束
            period.setStatus((byte) 2);
        } else {
            // 进行中
            period.setStatus((byte) 1);
        }

        // 7. 设置创建人和创建时间
        period.setCreateBy(1L); // 默认创建人ID，实际应用中应该从当前登录用户中获取
        period.setCreateTime(LocalDateTime.now());

        // 8. 保存评教周期
        boolean saved = this.save(period);
        if (!saved) {
            throw new CustomException("创建评教周期失败");
        }

        // 9. 返回创建后的评教周期DTO
        return evaluationPeriodDTOMapper.toDto(period, semester);
    }

    @Override
    public EvaluationPeriodDTO updatePeriod(Long periodId, PeriodFormDataDTO periodFormDataDTO) {
        // 1. 检查评教周期是否存在
        EvaluationPeriod period = this.getById(periodId);
        if (period == null) {
            throw new CustomException("评教周期不存在: " + periodId);
        }

        // 2. 根据学期名称查询学期ID
        Semester semester = semesterService.lambdaQuery()
                .eq(Semester::getSemesterName, periodFormDataDTO.getSemester())
                .one();

        if (semester == null) {
            throw new CustomException("学期不存在: " + periodFormDataDTO.getSemester());
        }

        // 3. 如果学期ID发生变化，检查新学期是否已经存在评教周期
        if (!period.getSemesterId().equals(semester.getSemesterId())) {
            boolean exists = this.lambdaQuery()
                    .eq(EvaluationPeriod::getSemesterId, semester.getSemesterId())
                    .ne(EvaluationPeriod::getPeriodId, periodId)
                    .exists();

            if (exists) {
                throw new CustomException("该学期已存在评教周期，不能重复创建");
            }
        }

        // 4. 解析开始时间和结束时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime;
        LocalDateTime endTime;

        try {
            startTime = LocalDateTime.parse(periodFormDataDTO.getStart_time(), formatter);
            endTime = LocalDateTime.parse(periodFormDataDTO.getEnd_time(), formatter);
        } catch (Exception e) {
            throw new CustomException("时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        }

        // 5. 检查开始时间是否早于结束时间
        if (startTime.isAfter(endTime)) {
            throw new CustomException("开始时间不能晚于结束时间");
        }

        // 6. 更新评教周期
        period.setSemesterId(semester.getSemesterId());
        period.setStartTime(startTime);
        period.setEndTime(endTime);

        // 7. 设置状态：根据当前时间判断状态
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(startTime)) {
            // 未开始
            period.setStatus((byte) 0);
        } else if (now.isAfter(endTime)) {
            // 已结束
            period.setStatus((byte) 2);
        } else {
            // 进行中
            period.setStatus((byte) 1);
        }

        // 8. 设置更新时间
        period.setUpdateTime(LocalDateTime.now());

        // 9. 保存更新后的评教周期
        boolean updated = this.updateById(period);
        if (!updated) {
            throw new CustomException("更新评教周期失败");
        }

        // 10. 返回更新后的评教周期DTO
        return evaluationPeriodDTOMapper.toDto(period, semester);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePeriod(Long periodId) {
        // 1. 检查评教周期是否存在
        EvaluationPeriod period = this.getById(periodId);
        if (period == null) {
            throw new CustomException("评教周期不存在: " + periodId);
        }

        // 2. 检查是否有关联的评教任务
        boolean hasRelatedTasks = evaluationTaskService.lambdaQuery()
                .eq(EvaluationTask::getPeriodId, periodId)
                .exists();

        if (hasRelatedTasks) {
            throw new CustomException("该评教周期下存在评教任务，不能删除");
        }

        // 3. 删除评教周期
        boolean removed = this.removeById(periodId);
        if (!removed) {
            throw new CustomException("删除评教周期失败");
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PublishTaskResultDTO publishTasks(Long periodId) {
        // 1. 检查评教周期是否存在
        EvaluationPeriod period = this.getById(periodId);
        if (period == null) {
            throw new CustomException("评教周期不存在: " + periodId);
        }

        // 2. 检查评教周期状态
        if (period.getStatus() == 2) {
            throw new CustomException("该评教周期已结束，不能发布评教任务");
        }

        // 3. 获取学期信息
        Long semesterId = period.getSemesterId();
        Semester semester = semesterService.getById(semesterId);
        if (semester == null) {
            throw new CustomException("学期信息不存在");
        }

        // 4. 查询该学期下的所有选课记录
        List<StudentCourse> studentCourses = studentCourseService.lambdaQuery()
                .eq(StudentCourse::getSemesterId, semesterId)
                .eq(StudentCourse::getStatus, 1) // 状态为1表示正常选课
                .list();

        if (studentCourses.isEmpty()) {
            throw new CustomException("该学期下没有选课记录，无法发布评教任务");
        }

        // 5. 创建评教任务
        int taskCount = 0;
        List<EvaluationTask> taskList = new ArrayList<>();

        for (StudentCourse sc : studentCourses) {
            // 检查是否已存在相同的评教任务
            boolean exists = evaluationTaskService.lambdaQuery()
                    .eq(EvaluationTask::getPeriodId, periodId)
                    .eq(EvaluationTask::getStudentId, sc.getStudentId())
                    .eq(EvaluationTask::getTeacherId, sc.getTeacherId())
                    .eq(EvaluationTask::getCourseId, sc.getCourseId())
                    .exists();

            if (!exists) {
                EvaluationTask task = new EvaluationTask();
                task.setPeriodId(periodId);
                task.setStudentId(sc.getStudentId());
                task.setTeacherId(sc.getTeacherId());
                task.setCourseId(sc.getCourseId());
                task.setStatus((byte) 0); // 0-未评教
                task.setCreateTime(LocalDateTime.now());

                taskList.add(task);
                taskCount++;
            }
        }

        // 批量保存评教任务
        if (!taskList.isEmpty()) {
            evaluationTaskService.saveBatch(taskList);
        }

        // 6. 如果评教周期状态为0（未开始），则更新为1（进行中）
        if (period.getStatus() == 0) {
            period.setStatus((byte) 1);
            period.setUpdateTime(LocalDateTime.now());
            this.updateById(period);
        }

        // 7. 返回结果
        PublishTaskResultDTO result = new PublishTaskResultDTO();
        result.setTaskCount(taskCount);

        return result;
    }

    @Override
    public List<EvaluationPeriodListDTO> getAllPeriods() {
        // 1. 查询所有评教周期，关联学期表获取学期名称
        MPJLambdaWrapper<EvaluationPeriod> wrapper = new MPJLambdaWrapper<EvaluationPeriod>()
                .selectAll(EvaluationPeriod.class)
                .select(Semester::getSemesterName)
                .leftJoin(Semester.class, Semester::getSemesterId, EvaluationPeriod::getSemesterId)
                .orderByDesc(EvaluationPeriod::getCreateTime);

        List<Map<String, Object>> resultMaps = baseMapper.selectJoinMaps(wrapper);

        // 2. 转换为DTO
        List<EvaluationPeriodListDTO> result = new ArrayList<>();
        for (Map<String, Object> map : resultMaps) {
            EvaluationPeriodListDTO dto = new EvaluationPeriodListDTO();
            dto.setId(((Long) map.get("period_id")).intValue());
            dto.setName((String) map.get("semester_name"));

            // 格式化时间
            LocalDateTime startTime = (LocalDateTime) map.get("start_time");
            LocalDateTime endTime = (LocalDateTime) map.get("end_time");
            if (startTime != null) {
                dto.setStart_time(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (endTime != null) {
                dto.setEnd_time(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }

            // 状态转换
            Byte status = (Byte) map.get("status");
            if (status != null) {
                dto.setStatus(status.intValue());
            }

            result.add(dto);
        }

        return result;
    }
}
