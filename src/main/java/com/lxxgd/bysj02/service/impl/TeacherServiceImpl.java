package com.lxxgd.bysj02.service.impl;

import com.lxxgd.bysj02.dto.TeacherCourseInfoDTO;
import com.lxxgd.bysj02.dto.TeacherDTO;
import com.lxxgd.bysj02.dto.UpdateTeacherInfoDTO;
import com.lxxgd.bysj02.dto.mapper.TeacherCourseInfoDTOMapper;
import com.lxxgd.bysj02.dto.mapper.TeacherDTOMapper;
import com.lxxgd.bysj02.common.exception.CustomException;
import com.lxxgd.bysj02.entity.Course;
import com.lxxgd.bysj02.entity.Department;
import com.lxxgd.bysj02.entity.Teacher;
import com.lxxgd.bysj02.entity.TeachingCourse;
import com.lxxgd.bysj02.mapper.TeacherMapper;
import com.lxxgd.bysj02.service.ICourseService;
import com.lxxgd.bysj02.service.IDepartmentService;
import com.lxxgd.bysj02.service.ITeacherService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lxxgd.bysj02.service.ITeachingCourseService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 教师信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Service
public class TeacherServiceImpl extends ServiceImpl<TeacherMapper, Teacher> implements ITeacherService {

    private static final Logger log = LoggerFactory.getLogger(TeacherServiceImpl.class);

    @Resource
    private TeacherCourseInfoDTOMapper teacherCourseInfoDTOMapper;

    @Resource
    private TeacherDTOMapper teacherDTOMapper;

    @Resource
    private ICourseService courseService;

    @Resource
    private IDepartmentService departmentService;

    @Resource
    private ITeachingCourseService teachingCourseService;

    @Override
    public TeacherDTO getTeacherById(Long teacherId) {
        Teacher teacher = this.lambdaQuery().eq(Teacher::getTeacherId, teacherId).one();

        if (teacher == null) {
            log.warn("未找到 ID 为 {} 的教师信息",teacherId);
            return null;
        }

        try {
            // 3. 设置新的更新时间
            teacher.setUpdateTime(LocalDateTime.now());
            // 4. 将更新持久化到数据库
            // 使用 ServiceImpl 提供的 updateById 方法
            boolean updated = this.updateById(teacher);
            if (!updated) {
                // 记录更新失败的日志，这通常不应该发生，除非记录在查询后被极其快速地删除了
                log.error("尝试更新教师 {} 的 update_time 时失败（记录可能不存在或更新条件冲突）", teacherId);
                // 可以根据业务决定是否继续返回旧数据或抛出异常，这里选择继续
            } else {
                log.debug("教师 {} 的 update_time 已更新", teacherId); // 调试日志
            }
        } catch (Exception e) {
            // 记录更新时发生的任何异常
            log.error("更新教师 {} 的 update_time 时发生异常", teacherId, e);
            // 根据业务决定是否继续，这里选择继续提供查询到的原始数据（更新时间未变）
        }

        List<TeachingCourse> teachingCourseList = this.teachingCourseService.lambdaQuery()
                .eq(TeachingCourse::getTeacherId, teacherId)
                .list();

        if (teachingCourseList == null) {
            teachingCourseList = Collections.emptyList();
        }

        Department department = null; // 初始化为 null
        if (teacher.getDepartmentId() != null) { // 先检查教师的 departmentId 是否为 null
            // 使用 getById 更简洁高效
            department = this.departmentService.lambdaQuery()
                    .eq(Department::getDepartmentId, teacher.getDepartmentId())
                    .one();

            if (department == null) {
                log.warn("教师 {} 的院系 ID {} 未找到对应的院系信息", teacherId, teacher.getDepartmentId());
            }
        } else {
            log.warn("教师 {} 的 departmentId 为 null", teacherId);
        }

        return teacherDTOMapper.toDto(teacher, teachingCourseList, department);
    }

    @Override
    public TeacherDTO getTeacherByUserId(Long userId) {
        return null;
    }

    @Override
    public TeacherDTO getTeacherByCourseId(Long courseId) {
        return null;
    }

    @Override
    public TeacherDTO getTeacherByDepartmentId(Long departmentId) {
        return null;
    }

    @Override
    public List<TeacherCourseInfoDTO> getCoursesByTeacherId(Long teacherId) {
        List<TeachingCourse> teachingCourseList = teachingCourseService.lambdaQuery().eq(TeachingCourse::getTeacherId, teacherId).list();

        List<TeacherCourseInfoDTO> teacherCourseInfoDTOList = new ArrayList<>();

        teachingCourseList.forEach(teachingCourse -> {
            Course course = courseService.lambdaQuery().eq(Course::getCourseId, teachingCourse.getCourseId()).one();

            TeacherCourseInfoDTO teacherCourseInfoDTO = teacherCourseInfoDTOMapper.toDto(course);

            teacherCourseInfoDTOList.add(teacherCourseInfoDTO);
        });

        return teacherCourseInfoDTOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeacherDTO updateTeacherInfo(UpdateTeacherInfoDTO updateTeacherInfoDTO) {
        // 1. 检查教师是否存在
        Teacher existingTeacher = this.getById(updateTeacherInfoDTO.getTeacherId());
        if (existingTeacher == null) {
            throw new CustomException("教师不存在");
        }

        // 2. 更新教师基本信息
        Teacher teacher = new Teacher();
        teacher.setTeacherId(updateTeacherInfoDTO.getTeacherId());
        teacher.setTeacherCode(updateTeacherInfoDTO.getTeacherCode());
        teacher.setDepartmentId(updateTeacherInfoDTO.getDepartmentId());
        teacher.setTitle(updateTeacherInfoDTO.getTitle());
        teacher.setTeachingYears(updateTeacherInfoDTO.getTeachingYears());
        teacher.setUpdateTime(LocalDateTime.now());

        // 3. 保存更新后的教师信息
        boolean updated = this.updateById(teacher);
        if (!updated) {
            throw new CustomException("更新教师信息失败");
        }

        // 4. 处理教师课程关联（如果提供了teachingCourseIds）
        List<Long> teachingCourseIds = updateTeacherInfoDTO.getTeachingCourseIds();
        if (teachingCourseIds != null) {
            // 4.1 获取当前教师的所有课程关联
            List<TeachingCourse> existingCourses = teachingCourseService.lambdaQuery()
                    .eq(TeachingCourse::getTeacherId, teacher.getTeacherId())
                    .list();

            // 4.2 获取当前教师的所有课程ID
            List<Long> existingCourseIds = existingCourses.stream()
                    .map(TeachingCourse::getCourseId)
                    .collect(Collectors.toList());

            // 4.3 找出需要添加的课程ID
            List<Long> coursesToAdd = teachingCourseIds.stream()
                    .filter(id -> !existingCourseIds.contains(id))
                    .collect(Collectors.toList());

            // 4.4 找出需要删除的课程关联
            List<TeachingCourse> coursesToRemove = existingCourses.stream()
                    .filter(course -> !teachingCourseIds.contains(course.getCourseId()))
                    .collect(Collectors.toList());

            // 4.5 删除不再需要的课程关联
            if (!coursesToRemove.isEmpty()) {
                List<Long> idsToRemove = coursesToRemove.stream()
                        .map(TeachingCourse::getId)
                        .collect(Collectors.toList());
                teachingCourseService.removeByIds(idsToRemove);
            }

            // 4.6 添加新的课程关联
            if (!coursesToAdd.isEmpty()) {
                List<TeachingCourse> newCourses = new ArrayList<>();
                for (Long courseId : coursesToAdd) {
                    TeachingCourse newCourse = new TeachingCourse();
                    newCourse.setTeacherId(teacher.getTeacherId());
                    newCourse.setCourseId(courseId);
                    // 使用默认学期ID，实际应用中可能需要从配置或参数中获取
                    newCourse.setSemesterId(1L);
                    newCourse.setCreateTime(LocalDateTime.now());
                    newCourses.add(newCourse);
                }
                teachingCourseService.saveBatch(newCourses);
            }
        }

        // 5. 获取更新后的教师信息并返回
        return getTeacherById(teacher.getTeacherId());
    }

}
