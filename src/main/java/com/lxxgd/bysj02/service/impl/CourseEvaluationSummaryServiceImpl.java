package com.lxxgd.bysj02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lxxgd.bysj02.dto.CourseSummaryDataDTO;
import com.lxxgd.bysj02.entity.CourseEvaluationSummary;
import com.lxxgd.bysj02.mapper.CourseEvaluationSummaryMapper;
import com.lxxgd.bysj02.service.ICourseEvaluationSummaryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * 课程评教统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
@Service
public class CourseEvaluationSummaryServiceImpl extends ServiceImpl<CourseEvaluationSummaryMapper, CourseEvaluationSummary> implements ICourseEvaluationSummaryService {

    @Override
    public CourseSummaryDataDTO getCourseEvaluationSummary(Long periodId, Long teacherId, Long courseId) {
        // 1. 查询指定课程的评价汇总数据
        LambdaQueryWrapper<CourseEvaluationSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CourseEvaluationSummary::getPeriodId, periodId)
                   .eq(CourseEvaluationSummary::getTeacherId, teacherId)
                   .eq(CourseEvaluationSummary::getCourseId, courseId);

        CourseEvaluationSummary summary = this.getOne(queryWrapper);

        if (summary == null) {
            // 如果没有找到汇总数据，返回空数据
            CourseSummaryDataDTO result = new CourseSummaryDataDTO();
            result.setPeriod_id(periodId.intValue());
            result.setCourse_id(courseId.intValue());
            result.setTotal_score(BigDecimal.ZERO);
            result.setTotal_count(0);
            result.setRanking(null);
            return result;
        }

        // 2. 计算排名：查询同一周期内所有课程的总得分，按得分降序排列
        LambdaQueryWrapper<CourseEvaluationSummary> rankingWrapper = new LambdaQueryWrapper<>();
        rankingWrapper.eq(CourseEvaluationSummary::getPeriodId, periodId)
                     .orderByDesc(CourseEvaluationSummary::getTotalScore);

        List<CourseEvaluationSummary> allSummaries = this.list(rankingWrapper);

        // 计算当前课程的排名
        String ranking = null;
        for (int i = 0; i < allSummaries.size(); i++) {
            CourseEvaluationSummary item = allSummaries.get(i);
            if (item.getTeacherId().equals(teacherId) && item.getCourseId().equals(courseId)) {
                ranking = String.valueOf(i + 1);
                break;
            }
        }

        // 3. 构建返回结果
        CourseSummaryDataDTO result = new CourseSummaryDataDTO();
        result.setSummary_id(summary.getSummaryId().intValue());
        result.setPeriod_id(summary.getPeriodId().intValue());
        result.setCourse_id(summary.getCourseId().intValue());
        result.setTotal_score(summary.getTotalScore());
        result.setTotal_count(summary.getTotalCount());
        result.setRanking(ranking);

        // 格式化时间
        if (summary.getCreateTime() != null) {
            result.setCreate_time(summary.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (summary.getUpdateTime() != null) {
            result.setUpdate_time(summary.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }

        return result;
    }
}
