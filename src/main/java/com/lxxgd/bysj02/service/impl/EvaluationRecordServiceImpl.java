package com.lxxgd.bysj02.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lxxgd.bysj02.dto.PaginatedCommentsDTO;
import com.lxxgd.bysj02.dto.StudentCommentDTO;
import com.lxxgd.bysj02.entity.EvaluationRecord;
import com.lxxgd.bysj02.entity.EvaluationTask;
import com.lxxgd.bysj02.entity.User;
import com.lxxgd.bysj02.mapper.EvaluationRecordMapper;
import com.lxxgd.bysj02.mapper.EvaluationTaskMapper;
import com.lxxgd.bysj02.service.IEvaluationRecordService;
import com.lxxgd.bysj02.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 评教记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
@Service
@Slf4j
public class EvaluationRecordServiceImpl extends ServiceImpl<EvaluationRecordMapper, EvaluationRecord> implements IEvaluationRecordService {

    @Resource
    private EvaluationTaskMapper evaluationTaskMapper;

    @Resource
    private IUserService userService;

    @Override
    public PaginatedCommentsDTO getCourseEvaluationComments(Long periodId, Long teacherId, Long courseId,
                                                          Integer page, Integer size, String keyword) {
        try {
            // 1. 查询符合条件的评教任务
            LambdaQueryWrapper<EvaluationTask> taskQueryWrapper = new LambdaQueryWrapper<>();
            taskQueryWrapper.eq(EvaluationTask::getPeriodId, periodId)
                           .eq(EvaluationTask::getTeacherId, teacherId)
                           .eq(EvaluationTask::getCourseId, courseId)
                           .eq(EvaluationTask::getStatus, 1); // 只查询已完成的评教任务

            List<EvaluationTask> tasks = evaluationTaskMapper.selectList(taskQueryWrapper);

            if (tasks.isEmpty()) {
                // 没有找到评教任务，返回空结果
                PaginatedCommentsDTO result = new PaginatedCommentsDTO();
                result.setItems(new ArrayList<>());
                result.setTotal(0L);
                result.setPage(page);
                result.setSize(size);
                return result;
            }

            // 2. 获取任务ID列表
            Set<Long> taskIds = tasks.stream()
                    .map(EvaluationTask::getTaskId)
                    .collect(Collectors.toSet());

            // 3. 查询评教记录（分页）
            Page<EvaluationRecord> recordPage = new Page<>(page, size);
            LambdaQueryWrapper<EvaluationRecord> recordQueryWrapper = new LambdaQueryWrapper<>();
            recordQueryWrapper.in(EvaluationRecord::getTaskId, taskIds)
                             .isNotNull(EvaluationRecord::getComment) // 只查询有评论的记录
                             .ne(EvaluationRecord::getComment, ""); // 排除空评论

            // 如果有关键词搜索，添加搜索条件
            if (StringUtils.hasText(keyword)) {
                recordQueryWrapper.like(EvaluationRecord::getComment, keyword);
            }

            recordQueryWrapper.orderByDesc(EvaluationRecord::getSubmitTime);

            Page<EvaluationRecord> recordPageResult = this.page(recordPage, recordQueryWrapper);

            // 4. 构建返回结果
            List<StudentCommentDTO> comments = new ArrayList<>();

            if (!recordPageResult.getRecords().isEmpty()) {
                // 获取学生信息（用于显示学生姓名，如果需要匿名则不设置）
                Set<Long> studentIds = tasks.stream()
                        .map(EvaluationTask::getStudentId)
                        .collect(Collectors.toSet());

                Map<Long, User> studentMap = userService.listByIds(studentIds).stream()
                        .collect(Collectors.toMap(User::getUserId, user -> user));

                Map<Long, EvaluationTask> taskMap = tasks.stream()
                        .collect(Collectors.toMap(EvaluationTask::getTaskId, task -> task));

                for (EvaluationRecord record : recordPageResult.getRecords()) {
                    StudentCommentDTO comment = new StudentCommentDTO();
                    comment.setId(record.getRecordId());
                    comment.setComment_text(record.getComment());
                    comment.setRating(record.getTotalScore());

                    if (record.getSubmitTime() != null) {
                        comment.setCreated_at(record.getSubmitTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    }

                    // 获取学生信息（实现匿名功能：不设置student_name字段）
                    EvaluationTask task = taskMap.get(record.getTaskId());
                    if (task != null) {
                        User student = studentMap.get(task.getStudentId());
                        if (student != null) {
                            // 注释掉下面这行来实现匿名功能
                            // comment.setStudent_name(student.getRealName());
                        }
                    }

                    comments.add(comment);
                }
            }

            // 5. 构建分页结果
            PaginatedCommentsDTO result = new PaginatedCommentsDTO();
            result.setItems(comments);
            result.setTotal(recordPageResult.getTotal());
            result.setPage(page);
            result.setSize(size);

            return result;

        } catch (Exception e) {
            log.error("获取课程评价评论失败", e);
            throw new RuntimeException("获取课程评价评论失败: " + e.getMessage());
        }
    }
}
