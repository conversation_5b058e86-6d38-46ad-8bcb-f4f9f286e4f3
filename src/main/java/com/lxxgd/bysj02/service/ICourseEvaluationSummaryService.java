package com.lxxgd.bysj02.service;

import com.lxxgd.bysj02.dto.CourseSummaryDataDTO;
import com.lxxgd.bysj02.entity.CourseEvaluationSummary;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 课程评教统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08
 */
public interface ICourseEvaluationSummaryService extends IService<CourseEvaluationSummary> {

    /**
     * 获取指定教师课程的评价汇总数据
     * 包括总得分、评教人数和排名信息
     *
     * @param periodId 评教周期ID
     * @param teacherId 教师ID
     * @param courseId 课程ID
     * @return 课程评价汇总数据
     */
    CourseSummaryDataDTO getCourseEvaluationSummary(Long periodId, Long teacherId, Long courseId);
}
