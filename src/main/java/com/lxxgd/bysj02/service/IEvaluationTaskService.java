package com.lxxgd.bysj02.service;

import com.lxxgd.bysj02.entity.EvaluationTask;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 评教任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
public interface IEvaluationTaskService extends IService<EvaluationTask> {

    /**
     * 检查学生是否有当前评教任务
     * 判断逻辑：
     * 1. 评教任务表中是否存在该学生ID
     * 2. 如果存在，当前时间是否在对应评教周期的时间范围内
     *
     * @param studentId 学生ID
     * @return 是否有当前评教任务
     */
    boolean hasCurrentEvaluationTask(Long studentId);
}
