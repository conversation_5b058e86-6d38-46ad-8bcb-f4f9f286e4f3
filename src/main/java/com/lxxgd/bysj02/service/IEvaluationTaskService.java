package com.lxxgd.bysj02.service;

import com.lxxgd.bysj02.dto.CourseInfoDTO;
import com.lxxgd.bysj02.dto.EvaluationDimensionDetailDTO;
import com.lxxgd.bysj02.dto.TeacherInfoDTO;
import com.lxxgd.bysj02.entity.EvaluationTask;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 评教任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
public interface IEvaluationTaskService extends IService<EvaluationTask> {

    /**
     * 检查学生是否有当前评教任务
     * 判断逻辑：
     * 1. 评教任务表中是否存在该学生ID
     * 2. 如果存在，当前时间是否在对应评教周期的时间范围内
     *
     * @param studentId 学生ID
     * @return 是否有当前评教任务
     */
    boolean hasCurrentEvaluationTask(Long studentId);

    /**
     * 获取学生待评价的教师列表
     * 从evaluation_task表中查找当前studentId对应的teacherId，
     * 进一步找到对应的待评价教师列表
     *
     * @param studentId 学生ID
     * @return 待评价教师列表
     */
    List<TeacherInfoDTO> getEvaluationTeachersByStudentId(Long studentId);

    /**
     * 根据教师ID和学生ID获取可评价课程列表
     * 从evaluation_task表中查找studentId和teacherId对应的courseId，
     * 进一步查询返回课程列表
     *
     * @param studentId 学生ID
     * @param teacherId 教师ID
     * @return 可评价课程列表
     */
    List<CourseInfoDTO> getEvaluationCoursesByStudentAndTeacher(Long studentId, Long teacherId);

    /**
     * 获取当前生效的评价模板下的评价维度、指标和评分选项
     * 后端先查evaluation_template中生效的模板，然后查template_dimension表中对应的生效dimensionId，
     * 然后查evaluation_dimension表中查相关数据，然后再到evaluation_indicator中查dimensionId对应相关数据
     *
     * @return 评价维度详情列表
     */
    List<EvaluationDimensionDetailDTO> getCurrentEvaluationDimensions();
}
