package com.lxxgd.bysj02.service;

import com.lxxgd.bysj02.dto.AddUserDTO;
import com.lxxgd.bysj02.dto.LoginDTO;
import com.lxxgd.bysj02.dto.UpdatePasswordDTO;
import com.lxxgd.bysj02.dto.UpdateUserInfoDTO;
import com.lxxgd.bysj02.dto.UserDTO;
import com.lxxgd.bysj02.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lxxgd.bysj02.vo.AccountUserVO;
import com.lxxgd.bysj02.vo.LoginVO;
import com.lxxgd.bysj02.vo.UserPasswordVO;

import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
public interface IUserService extends IService<User> {

    UserDTO getUserById(Long userId);

    LoginVO login(LoginDTO loginDTO);

    /**
     * 根据角色ID获取用户列表
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<AccountUserVO> getUserByRoleId(Long roleId);

    /**
     * 添加用户
     * 根据角色ID添加用户到user表和相应的学生/教师表
     * @param addUserDTO 添加用户DTO
     * @return 添加的用户信息
     */
    AccountUserVO addUser(AddUserDTO addUserDTO);

    /**
     * 删除用户
     * 根据用户ID删除用户信息，包括user表和相应的学生/教师表中的信息
     * @param userIds 用户ID列表
     * @return 是否删除成功
     */
    boolean deleteUsers(List<Long> userIds);

    /**
     * 更新用户信息
     * @param updateUserInfoDTO 更新用户信息DTO
     * @return 更新后的用户信息
     */
    UserDTO updateUserInfo(UpdateUserInfoDTO updateUserInfoDTO);

    /**
     * 获取用户密码
     * @param userId 用户ID
     * @return 用户密码信息
     */
    UserPasswordVO getUserPassword(Long userId);

    /**
     * 更新用户密码
     * @param userId 用户ID
     * @param updatePasswordDTO 更新密码DTO
     * @return 更新后的用户密码信息
     */
    UserPasswordVO updateUserPassword(Long userId, UpdatePasswordDTO updatePasswordDTO);
}
