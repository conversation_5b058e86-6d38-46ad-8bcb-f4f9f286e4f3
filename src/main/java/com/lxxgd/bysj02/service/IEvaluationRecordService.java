package com.lxxgd.bysj02.service;

import com.lxxgd.bysj02.dto.PaginatedCommentsDTO;
import com.lxxgd.bysj02.entity.EvaluationRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 评教记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
public interface IEvaluationRecordService extends IService<EvaluationRecord> {

    /**
     * 获取指定教师课程的学生评价评论列表
     * 支持分页和关键词搜索
     *
     * @param periodId 评教周期ID
     * @param teacherId 教师ID
     * @param courseId 课程ID
     * @param page 页码
     * @param size 每页大小
     * @param keyword 关键词搜索
     * @return 分页评论数据
     */
    PaginatedCommentsDTO getCourseEvaluationComments(Long periodId, Long teacherId, Long courseId,
                                                   Integer page, Integer size, String keyword);
}
