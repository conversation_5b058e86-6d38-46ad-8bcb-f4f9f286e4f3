package com.lxxgd.bysj02.service;

import com.lxxgd.bysj02.dto.RouteDTO;

import java.util.List;

public interface IRouteService {

    /**
     * 根据角色ID生成路由树
     * @param roleId 角色ID
     * @return 路由树结构
     */
    List<RouteDTO> buildRoutesByRole(Long roleId);

    /**
     * 根据角色编码构建路由树
     * @param roleCode 角色编码
     * @return 路由树列表 (根节点列表)
     */
    List<RouteDTO> buildRoutesByRoleCode(String roleCode); // 修改方法名和参数类型
}