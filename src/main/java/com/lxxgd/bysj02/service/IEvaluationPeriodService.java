package com.lxxgd.bysj02.service;

import com.lxxgd.bysj02.common.result.PageResult;
import com.lxxgd.bysj02.dto.EvaluationPeriodDTO;
import com.lxxgd.bysj02.dto.EvaluationPeriodListDTO;
import com.lxxgd.bysj02.dto.PeriodFormDataDTO;
import com.lxxgd.bysj02.dto.PeriodListParamsDTO;
import com.lxxgd.bysj02.dto.PublishTaskResultDTO;
import com.lxxgd.bysj02.entity.EvaluationPeriod;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 评教周期表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-09
 */
public interface IEvaluationPeriodService extends IService<EvaluationPeriod> {

    /**
     * 分页查询评教周期列表
     * @param params 查询参数
     * @return 分页结果
     */
    PageResult<EvaluationPeriodDTO> getPeriodList(PeriodListParamsDTO params);

    /**
     * 创建评教周期
     * @param periodFormDataDTO 评教周期表单数据
     * @return 创建后的评教周期DTO
     */
    EvaluationPeriodDTO createPeriod(PeriodFormDataDTO periodFormDataDTO);

    /**
     * 更新评教周期
     * @param periodId 评教周期ID
     * @param periodFormDataDTO 评教周期表单数据
     * @return 更新后的评教周期DTO
     */
    EvaluationPeriodDTO updatePeriod(Long periodId, PeriodFormDataDTO periodFormDataDTO);

    /**
     * 删除评教周期
     * @param periodId 评教周期ID
     * @return 是否删除成功
     */
    boolean deletePeriod(Long periodId);

    /**
     * 发布评教任务
     * @param periodId 评教周期ID
     * @return 发布结果，包含创建的任务数量
     */
    PublishTaskResultDTO publishTasks(Long periodId);

    /**
     * 获取所有评教周期列表
     * @return 评教周期列表
     */
    List<EvaluationPeriodListDTO> getAllPeriods();
}
