package com.lxxgd.bysj02.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lxxgd.bysj02.common.result.PageResult;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页工具类，用于处理 MyBatis Plus 的分页结果
 */
public class PageUtils {

    /**
     * 将 MyBatis Plus 的 Page 对象转换为自定义的 PageResult 对象
     *
     * @param page MyBatis Plus 的分页结果
     * @param <T>  数据类型
     * @return 自定义的分页结果
     */
    public static <T> PageResult<T> toPageResult(Page<T> page) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setList(page.getRecords());
        pageResult.setTotal(page.getTotal());
        return pageResult;
    }

    /**
     * 将 MyBatis Plus 的 Page 对象转换为自定义的 PageResult 对象，并对数据进行转换
     *
     * @param page    MyBatis Plus 的分页结果
     * @param mapper  数据转换函数
     * @param <T>     源数据类型
     * @param <R>     目标数据类型
     * @return 自定义的分页结果
     */
    public static <T, R> PageResult<R> toPageResult(Page<T> page, Function<T, R> mapper) {
        List<R> items = page.getRecords().stream()
                .map(mapper)
                .collect(Collectors.toList());

        PageResult<R> pageResult = new PageResult<>();
        pageResult.setList(items);
        pageResult.setTotal(page.getTotal());
        return pageResult;
    }

    /**
     * 将列表数据和总数转换为自定义的 PageResult 对象
     *
     * @param list  数据列表
     * @param total 总记录数
     * @param <T>   数据类型
     * @return 自定义的分页结果
     */
    public static <T> PageResult<T> toPageResult(List<T> list, long total) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(total);
        return pageResult;
    }

    /**
     * 创建空的分页结果
     *
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return toPageResult(List.of(), 0);
    }
}
