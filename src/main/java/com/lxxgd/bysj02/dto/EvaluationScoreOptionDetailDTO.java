package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 评分选项详情DTO（用于学生评教）
 */
@Data
@Schema(description = "评分选项详情DTO（用于学生评教）")
public class EvaluationScoreOptionDetailDTO {
    
    /**
     * 选项ID
     */
    @Schema(description = "选项ID")
    private String option_id;
    
    /**
     * 分值
     */
    @Schema(description = "分值")
    private BigDecimal score;
    
    /**
     * 分值描述
     */
    @Schema(description = "分值描述")
    private String score_description;
    
    /**
     * 排序号
     */
    @Schema(description = "排序号")
    private Integer sort;
}
