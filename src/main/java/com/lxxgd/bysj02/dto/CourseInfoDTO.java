package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 课程信息DTO（用于评教课程列表）
 */
@Data
@Schema(description = "课程信息DTO（用于评教课程列表）")
public class CourseInfoDTO {
    
    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private String courseId;
    
    /**
     * 课程名称
     */
    @Schema(description = "课程名称")
    private String courseName;
    
    /**
     * 课程类型(1-通识课 2-专业课 3-选修课)
     */
    @Schema(description = "课程类型(1-通识课 2-专业课 3-选修课)")
    private String courseType;
}
