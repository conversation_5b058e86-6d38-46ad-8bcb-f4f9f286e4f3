package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 评价维度详情DTO（用于学生评教）
 */
@Data
@Schema(description = "评价维度详情DTO（用于学生评教）")
public class EvaluationDimensionDetailDTO {
    
    /**
     * 维度ID
     */
    @Schema(description = "维度ID")
    private String dimension_id;
    
    /**
     * 维度名称
     */
    @Schema(description = "维度名称")
    private String dimension_name;
    
    /**
     * 维度权重
     */
    @Schema(description = "维度权重")
    private BigDecimal weight;
    
    /**
     * 该维度下的指标列表
     */
    @Schema(description = "该维度下的指标列表")
    private List<EvaluationIndicatorDetailDTO> indicators;
}
