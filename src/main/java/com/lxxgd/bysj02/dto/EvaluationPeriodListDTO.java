package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 评教周期列表DTO（用于前端列表显示）
 */
@Data
@Schema(description = "评教周期列表DTO（用于前端列表显示）")
public class EvaluationPeriodListDTO {
    
    /**
     * 周期ID
     */
    @Schema(description = "周期ID")
    private Integer id;
    
    /**
     * 学期名称
     */
    @Schema(description = "学期名称")
    private String name;
    
    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String start_time;
    
    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private String end_time;
    
    /**
     * 状态(0-未开始 1-进行中 2-已结束)
     */
    @Schema(description = "状态(0-未开始 1-进行中 2-已结束)")
    private Integer status;
}
