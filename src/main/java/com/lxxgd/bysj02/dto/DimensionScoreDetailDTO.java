package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 维度得分详情DTO
 */
@Data
@Schema(description = "维度得分详情DTO")
public class DimensionScoreDetailDTO {
    
    /**
     * 维度ID
     */
    @Schema(description = "维度ID")
    @NotBlank(message = "维度ID不能为空")
    private String dimension_id;
    
    /**
     * 维度得分
     */
    @Schema(description = "维度得分")
    @NotNull(message = "维度得分不能为空")
    private BigDecimal score;
}
