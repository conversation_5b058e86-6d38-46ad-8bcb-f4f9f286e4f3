package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 分页评论数据DTO
 */
@Data
@Schema(description = "分页评论数据DTO")
public class PaginatedCommentsDTO {
    
    /**
     * 评论列表
     */
    @Schema(description = "评论列表")
    private List<StudentCommentDTO> items;
    
    /**
     * 总记录数
     */
    @Schema(description = "总记录数")
    private Long total;
    
    /**
     * 当前页码
     */
    @Schema(description = "当前页码")
    private Integer page;
    
    /**
     * 每页大小
     */
    @Schema(description = "每页大小")
    private Integer size;
}
