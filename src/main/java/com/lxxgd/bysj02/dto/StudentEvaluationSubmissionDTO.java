package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 学生评价提交数据DTO
 */
@Data
@Schema(description = "学生评价提交数据DTO")
public class StudentEvaluationSubmissionDTO {

    /**
     * 评教任务ID
     */
    @Schema(description = "评教任务ID")
    @NotBlank(message = "评教任务ID不能为空")
    private String task_id;

    /**
     * 总得分
     */
    @Schema(description = "总得分")
    @NotNull(message = "总得分不能为空")
    private BigDecimal total_score;

    /**
     * 总体评价
     */
    @Schema(description = "总体评价")
    private String comment;

    /**
     * 评价详情列表
     */
    @Schema(description = "评价详情列表")
    @NotEmpty(message = "评价详情不能为空")
    @Valid
    private List<EvaluationRecordDetailDTO> details;

    /**
     * 各维度得分详情（可选）
     */
    @Schema(description = "各维度得分详情（可选）")
    @Valid
    private List<DimensionScoreDetailDTO> dimension_scores;
}
