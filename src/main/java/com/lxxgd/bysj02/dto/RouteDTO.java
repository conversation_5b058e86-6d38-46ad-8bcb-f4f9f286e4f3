package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "路由规则DTO")
public class RouteDTO {
    private String path;
    private Object meta;
    private List<RouteDTO> children;
    private String name;

    @Data
    @Schema(description = "路由规则元数据")
    public static class Meta {
        private String icon;
        private String title;
        private Integer rank;
        private Boolean showParent;
    }
}
