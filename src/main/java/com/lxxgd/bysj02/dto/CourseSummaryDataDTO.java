package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 课程评价汇总数据DTO
 */
@Data
@Schema(description = "课程评价汇总数据DTO")
public class CourseSummaryDataDTO {
    
    /**
     * 统计ID
     */
    @Schema(description = "统计ID")
    private Integer summary_id;
    
    /**
     * 评教周期ID
     */
    @Schema(description = "评教周期ID")
    private Integer period_id;
    
    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private Integer course_id;
    
    /**
     * 总得分
     */
    @Schema(description = "总得分")
    private BigDecimal total_score;
    
    /**
     * 评教人数
     */
    @Schema(description = "评教人数")
    private Integer total_count;
    
    /**
     * 排名
     */
    @Schema(description = "排名")
    private String ranking;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private String create_time;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private String update_time;
}
