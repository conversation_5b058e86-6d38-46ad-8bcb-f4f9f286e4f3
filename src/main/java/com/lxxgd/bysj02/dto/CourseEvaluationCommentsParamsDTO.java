package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 课程评价评论查询参数DTO
 */
@Data
@Schema(description = "课程评价评论查询参数DTO")
public class CourseEvaluationCommentsParamsDTO {
    
    /**
     * 教师用户ID
     */
    @Schema(description = "教师用户ID")
    @NotNull(message = "教师用户ID不能为空")
    private Integer userId;
    
    /**
     * 评教周期ID
     */
    @Schema(description = "评教周期ID")
    @NotNull(message = "评教周期ID不能为空")
    private Integer period_id;
    
    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    @NotNull(message = "课程ID不能为空")
    private Integer course_id;
    
    /**
     * 页码（可选，默认为1）
     */
    @Schema(description = "页码（可选，默认为1）")
    private Integer page = 1;
    
    /**
     * 每页大小（可选，默认为10）
     */
    @Schema(description = "每页大小（可选，默认为10）")
    private Integer size = 10;
    
    /**
     * 关键词搜索（可选）
     */
    @Schema(description = "关键词搜索（可选）")
    private String keyword;
}
