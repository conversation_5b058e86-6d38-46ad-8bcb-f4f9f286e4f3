package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 学生评价评论DTO
 */
@Data
@Schema(description = "学生评价评论DTO")
public class StudentCommentDTO {
    
    /**
     * 评价记录ID
     */
    @Schema(description = "评价记录ID")
    private Long id;
    
    /**
     * 学生姓名（匿名时不传）
     */
    @Schema(description = "学生姓名（匿名时不传）")
    private String student_name;
    
    /**
     * 评价内容
     */
    @Schema(description = "评价内容")
    private String comment_text;
    
    /**
     * 评分（可能有单项评分）
     */
    @Schema(description = "评分（可能有单项评分）")
    private BigDecimal rating;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private String created_at;
}
