package com.lxxgd.bysj02.dto.mapper;

import com.lxxgd.bysj02.dto.RouteDTO;
import com.lxxgd.bysj02.entity.Permission;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface RouteDTOMapper {

    @Mapping(target = "path", source = "path")
    @Mapping(target = "name", source = "permissionCode")
    @Mapping(target = "meta", source = ".", qualifiedByName = "buildMeta")
    @Mapping(target = "children", ignore = true)
    RouteDTO permissionToRouteDTO(Permission permission);

    @Named("buildMeta")
    default RouteDTO.Meta buildMeta(Permission permission) {
        RouteDTO.Meta meta = new RouteDTO.Meta();
        meta.setIcon(permission.getIcon());
        meta.setTitle(permission.getPermissionName());
        meta.setRank(permission.getSort());
        meta.setShowParent(permission.getParentId() == 0);
        return meta;
    }
}