package com.lxxgd.bysj02.dto.mapper;

import com.lxxgd.bysj02.dto.TeacherDTO;
import com.lxxgd.bysj02.entity.Department;
import com.lxxgd.bysj02.entity.Teacher;
import com.lxxgd.bysj02.entity.TeachingCourse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface TeacherDTOMapper {

    @Mapping(target = "teacherId", source = "teacher.teacherId")
    @Mapping(target = "departmentId", source = "teacher.departmentId")
    @Mapping(target = "departmentName", source = "department.departmentName")
    @Mapping(target = "title", source = "teacher.title")
    @Mapping(target = "teachingYears", source = "teacher.teachingYears")
    @Mapping(target = "teachingCourseIds", source = "teachingCourseList", qualifiedByName = "courseIds")
    @Mapping(target = "status", source = "teacher.status")
    @Mapping(target = "lastLoginTime", source = "teacher.updateTime")
    TeacherDTO toDto(Teacher teacher, List<TeachingCourse> teachingCourseList, Department department);

//    List<TeacherDTO> toDtoList(List<Teacher> teacherList);
//
//    Teacher toEntity(TeacherDTO teacherDTO);
//
//    List<Teacher> toEntityList(List<TeacherDTO> teacherDTOList);

    @Named("courseIds")
    default List<Long> mapCourseIds(List<TeachingCourse> teachingCourseList) {
        if (teachingCourseList == null) {
            return null;
        }
        return teachingCourseList.stream()
                .map(TeachingCourse::getCourseId)
                .collect(Collectors.toList());
    }
}
