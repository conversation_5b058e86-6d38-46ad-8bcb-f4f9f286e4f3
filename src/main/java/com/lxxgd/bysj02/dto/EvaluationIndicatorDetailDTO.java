package com.lxxgd.bysj02.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 评价指标详情DTO（用于学生评教）
 */
@Data
@Schema(description = "评价指标详情DTO（用于学生评教）")
public class EvaluationIndicatorDetailDTO {
    
    /**
     * 指标ID
     */
    @Schema(description = "指标ID")
    private String indicator_id;
    
    /**
     * 指标名称
     */
    @Schema(description = "指标名称")
    private String indicator_name;
    
    /**
     * 指标权重
     */
    @Schema(description = "指标权重")
    private BigDecimal weight;
    
    /**
     * 最高分
     */
    @Schema(description = "最高分")
    private BigDecimal max_score;
    
    /**
     * 最低分
     */
    @Schema(description = "最低分")
    private BigDecimal min_score;
    
    /**
     * 评分类型
     */
    @Schema(description = "评分类型")
    private String scoring_type;
    
    /**
     * 评分选项列表（仅当scoring_type为'option'或类似类型时存在）
     */
    @Schema(description = "评分选项列表")
    private List<EvaluationScoreOptionDetailDTO> options;
}
